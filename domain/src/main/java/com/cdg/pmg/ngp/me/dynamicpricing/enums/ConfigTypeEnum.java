package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ConfigTypeEnum {
  ALL("ALL"),
  FLAT_FARE("FLAT_FARE"),
  FARE_TYPE("FARE_TYPE"),
  COMPANY_HOLIDAY("COMPANY_HOLIDAY"),
  LOCATION_SURCHARGE("LOCATION_SURCHARGE"),
  DYNP_SURGE("DYNP_SURGE");

  private String value;

  ConfigTypeEnum(String value) {
    this.value = value;
  }
}
