package com.cdg.pmg.ngp.me.dynamicpricing.queries;

import java.io.Serial;
import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiFareRequestQuery implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String countryCode;
  private String bookingChannel;
  private String mobile;
  private String jobType;
  private String pickupAddressRef;
  private Double pickupAddressLat;
  private Double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private Double destAddressLat;
  private Double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private Double intermediateAddrLat;
  private Double intermediateAddrLng;
  private String intermediateZoneId;
  private List<Integer> vehTypeIDs;
  private OffsetDateTime fareDate;
  private Long regionId;
  private Long modelId;
  private String modelName;
}
