package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serializable;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocSurchargeAddressEntity implements Serializable {

  private String locationId;
  private String addressRef;

  @Override
  public String toString() {
    return "LocSurchargeAddressEntity{"
        + "locationId='"
        + locationId
        + '\''
        + ", addressRef='"
        + addressRef
        + '\''
        + '}';
  }
}
