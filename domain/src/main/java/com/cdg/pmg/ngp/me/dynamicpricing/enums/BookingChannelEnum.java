package com.cdg.pmg.ngp.me.dynamicpricing.enums;

import java.util.Set;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BookingChannelEnum {
  IPHONE("IPHONE"),
  ANDROID("ANDROID"),
  CSA("CSA"),
  SMS("SMS"),
  IVR("IVR"),
  IRD("IRD"),
  IRD_POSTAL("IRD_POSTAL"),
  OPENAPI("OPENAPI"),
  MWF("MWF"),
  H5ALIPAY("H5ALIPAY"),
  H5("H5"),
  H5DBSPAYLAH("H5DBSPAYLAH"),
  H5LAZADA("H5LAZADA"),
  H5KRISPLUS("H5KRISPLUS");

  private String value;

  BookingChannelEnum(String value) {
    this.value = value;
  }

  private static final Set<String> BOOKING_CHANNELS_NO_NEED_DRIVER_FEE =
      Set.of(
          BookingChannelEnum.IVR.getValue(),
          BookingChannelEnum.CSA.getValue(),
          BookingChannelEnum.SMS.getValue());

  public static boolean isNeedCalculateDriverFee(String bookingChannel) {
    /*
      If booking channel is empty,then need to calculate driver fee,
      because it is not "IVR","CSA" or "SMS".
    */
    if (bookingChannel == null) {
      return true;
    }

    // Return true if the booking channel is not in the above set ,or return false
    return !BOOKING_CHANNELS_NO_NEED_DRIVER_FEE.contains(bookingChannel.toUpperCase());
  }
}
