package com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FareCountAggregateResult {
  private Long startTimestamp;
  private Long endTimestamp;
  private Long h3RegionId;
  private String regionVersion;
  private SurgeAreaTypeEnum areaType;
  private Long modelId;
  private Long getFareCount;
}
