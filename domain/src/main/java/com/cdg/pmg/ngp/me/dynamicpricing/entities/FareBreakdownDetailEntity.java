package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FareBreakdownDetailEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1;

  private Integer flatFareBreakdownId;
  private String bookingId;
  private String fareId;
  private String tripId;
  private String pickupAddressRef;
  private Double pickupAddressLat;
  private Double pickupAddressLng;
  private String pickupZoneId;
  private String destAddressRef;
  private Double destAddressLat;
  private Double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private Double intermediateAddrLat;
  private Double intermediateAddrLng;
  private String intermediateZoneId;
  private Timestamp requestDate;
  private Double flagDownRate;
  private Double tier1Fare;
  private Double tier2Fare;
  private Double waitTimeFare;
  private Double peakHrFare;
  private Double midNightFare;
  private Double hourlySurcharge;
  private Double bookingFee;
  private Double locSurcharge;
  private Double eventSurcharge;
  private Double additionalSurcharge;
  private Double multiDestSurcharge;
  private Long routingDistance;
  private Long ett;
  private String encodedPolyline;
  private Double idealTime;
  private String calMethod;
  private Double dpSurgePercent;
  private Double dpSurgeAmount;
  private Double dpAppliedSurgeAmount;
  private Double dpBaseFareForSurge;
  private Double dpFinalFare;
  private Integer dpBatchKey;
  private Double meteredBaseFare;
  private BigDecimal totalFare;
  private BigDecimal estimatedFareLF;
  private BigDecimal estimatedFareRT;
  private Long flatPlatformFeeId;
  private Double flatPlatformFee;
  private Long meterPlatformFeeId;
  private Double meterPlatformFeeLower;
  private Double meterPlatformFeeUpper;
  private List<AdditionalChargeConfigItem> additionalCharges;
}
