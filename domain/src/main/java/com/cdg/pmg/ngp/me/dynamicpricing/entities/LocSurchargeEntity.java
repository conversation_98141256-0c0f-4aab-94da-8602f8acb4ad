package com.cdg.pmg.ngp.me.dynamicpricing.entities;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalTime;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocSurchargeEntity implements Serializable {
  @Serial private static final long serialVersionUID = -4281069377383347263L;

  private String locationId;
  private String locationName;
  private String fareType;
  private String chargeBy;
  private Double surchargeValue;
  private String productId;
  private LocalTime startTime;
  private LocalTime endTime;
  private String dayIndicator;

  @Override
  public String toString() {
    return "LocSurchargeEntity{"
        + "locationId="
        + locationId
        + ", locationName='"
        + locationName
        + '\''
        + ", fareType='"
        + fareType
        + '\''
        + ", chargeBy='"
        + chargeBy
        + '\''
        + ", surchargeValue="
        + surchargeValue
        + ", productId='"
        + productId
        + '\''
        + ", startTime="
        + startTime
        + ", endTime="
        + endTime
        + ", dayIndicator='"
        + dayIndicator
        + '\''
        + '}';
  }

  public boolean isValid(LocalTime time) {
    return this.startTime.isBefore(time) && this.endTime.isAfter(time);
  }
}
