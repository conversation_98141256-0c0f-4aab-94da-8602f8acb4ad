image:
  name: 350155334257.dkr.ecr.ap-southeast-1.amazonaws.com/baseimage-bitbucket:latest
  aws:
    oidc-role: arn:aws:iam::350155334257:role/bitbucket-pipelines-role

options:
  docker: true
  runtime:
    cloud:
      arch: arm

pipelines:

  # Automated pipelines

  branches:
    '{sit,uat1,sit-eks,uat1-eks}':
      - step:
          name: "Build and deploy"
          runs-on: ["self.hosted", "linux.arm64", "nebula"]
          oidc: true
          script:
            - task "common:init"
            - task "common:legacy-liquibase-deploy-eks"

  # Pull request pipelines

  pull-requests:
    '**':
      - step:
          name: "PR check"
          runs-on: ["self.hosted", "linux.arm64", "nebula"]
          oidc: true
          script:
            - task "common:init"
            - task "common:legacy-pr-check"

  # Manual pipelines

  custom:
    deploy-sit:
      - step:
          name: "Deploy SIT"
          runs-on: ["self.hosted", "linux.arm64", "nebula"]
          oidc: true
          script:
            - export TARGET_REVISION=sit
            - task "common:init"
            - task "common:legacy-liquibase-deploy-eks"

    deploy-uat:
      - step:
          name: "Deploy UAT"
          runs-on: ["self.hosted", "linux.arm64", "nebula"]
          oidc: true
          script:
            - export TARGET_REVISION=uat1
            - task "common:init"
            - task "common:legacy-liquibase-deploy-eks"

    deploy-prod:
      - step:
          name: "Deploy PROD"
          runs-on: ["self.hosted", "linux.arm64", "nebula"]
          oidc: true
          deployment: production
          script:
            - export TARGET_REVISION=master
            - task "common:init"
            - task "common:legacy-liquibase-deploy-eks-prod"
