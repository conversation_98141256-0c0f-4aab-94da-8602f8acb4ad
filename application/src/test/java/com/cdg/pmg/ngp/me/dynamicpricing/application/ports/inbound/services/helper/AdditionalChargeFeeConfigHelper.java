package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.helper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.AdditionalChargeFeeConfigResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.BookingChannelEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdditionalChargeFeeConfigHelper {
  private AdditionalChargeFeeConfigHelper() {}

  public static final String DRIVER_FEE = "DRIVER_FEE";
  public static final String BOOSTER_SEAT = "BOOSTER_SEAT";

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateAdditionalChargeConfig() {

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfig =
        new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    List<AdditionalChargeFeeConfigResponse> boosterSeatFeeConfigList = new ArrayList<>();
    additionalChargeFeeConfig.put(DRIVER_FEE, driverFeeConfigList);
    additionalChargeFeeConfig.put(BOOSTER_SEAT, boosterSeatFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse vehicleAttributeId =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.VEHICLE_ATTRIBUTE_ID)
            .chargeValue(140.0)
            .chargeDescription("vehicleAttributeId")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse isOptional =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_OPTIONAL)
            .chargeValue(1d)
            .chargeDescription("is optional")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatIsCountInTotalFare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatVehicleAttributeId =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.VEHICLE_ATTRIBUTE_ID)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeValue(140.0)
            .chargeDescription("vehicleAttributeId")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatIsOptional =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_OPTIONAL)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeValue(1d)
            .chargeDescription("is optional")
            .chargeFormula("true")
            .build();

    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    driverFeeConfigList.add(isCountInTotalfare);
    driverFeeConfigList.add(vehicleAttributeId);
    driverFeeConfigList.add(isOptional);
    boosterSeatFeeConfigList.add(boosterSeatChargeThreshold);
    boosterSeatFeeConfigList.add(boosterSeatChargeLowerValue);
    boosterSeatFeeConfigList.add(boosterSeatChargeUpperValue);
    boosterSeatFeeConfigList.add(boosterSeatIsCountInTotalFare);
    boosterSeatFeeConfigList.add(boosterSeatVehicleAttributeId);
    boosterSeatFeeConfigList.add(boosterSeatIsOptional);

    return additionalChargeFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigWithoutIsCountInTotalfare() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigIsCountInTotalfareFalse() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse chargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeValue(16.00)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeValue(0.30)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse chargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeValue(0.50)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("false")
            .build();
    driverFeeConfigList.add(chargeThreshold);
    driverFeeConfigList.add(chargeLowerValue);
    driverFeeConfigList.add(chargeUpperValue);
    driverFeeConfigList.add(isCountInTotalfare);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateDriverFeeConfigOnlyIsCountInTotalfareTrue() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> driverFeeConfig = new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    driverFeeConfig.put(DRIVER_FEE, driverFeeConfigList);

    AdditionalChargeFeeConfigResponse isCountInTotalfare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();
    driverFeeConfigList.add(isCountInTotalfare);
    return driverFeeConfig;
  }

  public static Map<String, List<AdditionalChargeFeeConfigResponse>>
      generateAdditionalChargeFeeConfigWithEmptyValue() {
    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfig =
        new HashMap<>();
    List<AdditionalChargeFeeConfigResponse> driverFeeConfigList = new ArrayList<>();
    List<AdditionalChargeFeeConfigResponse> boosterSeatFeeConfigList = new ArrayList<>();
    additionalChargeFeeConfig.put(DRIVER_FEE, driverFeeConfigList);
    additionalChargeFeeConfig.put(BOOSTER_SEAT, boosterSeatFeeConfigList);

    AdditionalChargeFeeConfigResponse driverFeeChargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .chargeDescription(
                "DRIVER_FEE, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse driverFeeChargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .chargeDescription(
                "DRIVER_FEE, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse driverFeeChargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .chargeDescription(
                "DRIVER_FEE, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse driverFeeIsCountInTotalFare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .chargeDescription(
                "DRIVER_FEE, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse driverFeeVehicleAttributeId =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.VEHICLE_ATTRIBUTE_ID)
            .chargeValue(140.0)
            .chargeDescription("vehicleAttributeId")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse driverFeeIsOptional =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(DRIVER_FEE)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_OPTIONAL)
            .chargeValue(1d)
            .chargeDescription("is optional")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeThreshold =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_THRESHOLD)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_threshold, The threshold for trigger the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeLowerValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_LOWER_VALUE)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_lower_value, The lower bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatChargeUpperValue =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.CHARGE_UPPER_VALUE)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, charge_upper_value, The upper bond value for the additional charge")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatIsCountInTotalFare =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_COUNT_IN_TOTALFARE)
            .chargeValue(0.00)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeDescription(
                "BOOSTER_SEAT, is_count_in_totalfare, The additional charge value is counted into the total fare")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatVehicleAttributeId =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.VEHICLE_ATTRIBUTE_ID)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeValue(140.0)
            .chargeDescription("vehicleAttributeId")
            .chargeFormula("true")
            .build();

    AdditionalChargeFeeConfigResponse boosterSeatIsOptional =
        AdditionalChargeFeeConfigResponse.builder()
            .chargeId(1)
            .chargeType(BOOSTER_SEAT)
            .chargeKey(DriverFeeAdditionalChargeProcessor.IS_OPTIONAL)
            .bookingChannel(BookingChannelEnum.IPHONE.getValue())
            .chargeValue(1d)
            .chargeDescription("is optional")
            .chargeFormula("true")
            .build();

    driverFeeConfigList.add(driverFeeChargeThreshold);
    driverFeeConfigList.add(driverFeeChargeLowerValue);
    driverFeeConfigList.add(driverFeeChargeUpperValue);
    driverFeeConfigList.add(driverFeeIsCountInTotalFare);
    driverFeeConfigList.add(driverFeeVehicleAttributeId);
    driverFeeConfigList.add(driverFeeIsOptional);
    boosterSeatFeeConfigList.add(boosterSeatChargeThreshold);
    boosterSeatFeeConfigList.add(boosterSeatChargeLowerValue);
    boosterSeatFeeConfigList.add(boosterSeatChargeUpperValue);
    boosterSeatFeeConfigList.add(boosterSeatIsCountInTotalFare);
    boosterSeatFeeConfigList.add(boosterSeatVehicleAttributeId);
    boosterSeatFeeConfigList.add(boosterSeatIsOptional);
    return additionalChargeFeeConfig;
  }
}
