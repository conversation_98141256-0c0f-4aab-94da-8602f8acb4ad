package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.helper.AdditionalChargeFeeConfigHelper;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.BookingChannelEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DriverFeeAdditionalChargeProcessorTest {

  @Mock private DriverFeeAdditionalChargeProcessor driverFeeAdditionalChargeProcessor;

  @BeforeEach
  void setUp() {
    driverFeeAdditionalChargeProcessor = new DriverFeeAdditionalChargeProcessor();
  }

  @Test
  void driverFeeCalculateNull_WithoutIsCountInTotalfare() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigWithoutIsCountInTotalfare(),
            true,
            null);
    assertTrue(CollectionUtils.isEmpty(additionalChargeDriverFeeData));
  }

  @Test
  void driverFeeCalculateNull_IsCountInTotalfareFalse() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigIsCountInTotalfareFalse(),
            true,
            null);
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateNull_WithEmptyDriverFeeConfig() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(origin, Map.of(), true, null);
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateValueNull_WithOnlyIsCountInTotalfareTrue() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateDriverFeeConfigOnlyIsCountInTotalfareTrue(),
            true,
            null);
    assertFalse(additionalChargeDriverFeeData.isEmpty());
    assertNull(additionalChargeDriverFeeData.get(0).getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareRTDriverFee());
  }

  @Test
  void driverFeeCalculateValueNull_WithConfigValueEmpty() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateAdditionalChargeFeeConfigWithEmptyValue(),
            true,
            null);
    assertFalse(additionalChargeDriverFeeData.isEmpty());
    assertNull(additionalChargeDriverFeeData.get(0).getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareRTDriverFee());
  }

  @Test
  void boosterSeatCalculateValueNull_WithConfigValue() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateAdditionalChargeConfig(),
            false,
            BookingChannelEnum.IPHONE.getValue());
    assertFalse(additionalChargeDriverFeeData.isEmpty());
    assertNull(additionalChargeDriverFeeData.get(0).getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareRTDriverFee());
  }

  @Test
  void driverFeeCalculateValueNull_WithNullOriginFareValue() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(null);
    origin.setEstimatedFareLF(null);
    origin.setEstimatedFareRT(null);
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin, AdditionalChargeFeeConfigHelper.generateAdditionalChargeConfig(), true, null);
    assertFalse(additionalChargeDriverFeeData.isEmpty());
    assertNull(additionalChargeDriverFeeData.get(0).getTotalFareDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareLFDriverFee());
    assertNull(additionalChargeDriverFeeData.get(0).getEstimatedFareRTDriverFee());
  }

  @Test
  void givenNullCalculateParam_whenCalculateAdditionalCharge_thenReturnAnEmptyList() {
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            null, AdditionalChargeFeeConfigHelper.generateAdditionalChargeConfig(), true, null);
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void givenEmptyAdditionalChargeFee_whenCalculateAdditionalCharge_thenReturnAnEmptyList() {
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            FlatFareVOPart.builder().build(),
            Map.of(AdditionalChargeFeeConfigHelper.DRIVER_FEE, List.of()),
            true,
            null);
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }

  @Test
  void driverFeeCalculateValueNull_WithIsNeedCalculateDriverFeeIsFalse() {

    FlatFareVOPart origin = new FlatFareVOPart();
    origin.setTotalFare(BigDecimal.valueOf(13.0));
    origin.setEstimatedFareLF(BigDecimal.valueOf(12.0));
    origin.setEstimatedFareRT(BigDecimal.valueOf(15.0));
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeData =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            origin,
            AdditionalChargeFeeConfigHelper.generateAdditionalChargeFeeConfigWithEmptyValue(),
            false,
            null);
    assertTrue(additionalChargeDriverFeeData.isEmpty());
  }
}
