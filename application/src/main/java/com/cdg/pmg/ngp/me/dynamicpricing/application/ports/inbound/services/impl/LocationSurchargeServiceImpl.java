package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.SurchargeUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.LocationSurchargeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeAddressEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class LocationSurchargeServiceImpl implements LocationSurchargeService {
  private static final String LOC_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.LOC_SURC)
          .concat(RedisKeyConstant.COLON);

  private static final String LOC_SURCHARGE_ADDRESS_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.LOC_SURCHARGE)
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.ADDRESS)
          .concat(RedisKeyConstant.COLON);

  private static final String LOC_SURCHARGE_SURCHARGE_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.LOC_SURCHARGE)
          .concat(RedisKeyConstant.COLON)
          .concat(RedisKeyConstant.SURCHARGE)
          .concat(RedisKeyConstant.COLON);

  private final LocationSurchargeConfigRepository locationSurchargeConfigRepository;
  private final CacheService cacheService;
  private final FlatFareConfigService flatFareConfigService;

  @Override
  public void loadLocSurchargeAddresses(String locationId) {
    List<LocSurchargeAddressEntity> locAddresses =
        filterLocSurchargeAddresses(
            locationSurchargeConfigRepository.getLocSurchargeAddresses(), locationId);

    Map<String, List<LocSurchargeAddressEntity>> mapLocAddresses =
        SurchargeUtils.groupLocSurchargeAddress(locAddresses);

    putAllLocSurchargeAddressesToCache(locationId, mapLocAddresses);
  }

  @Override
  public void loadLocSurchargeConfigs(String locationId) {
    List<LocSurchargeEntity> configs = locationSurchargeConfigRepository.getLocSurchargeConfigs();
    List<LocSurchargeEntity> filteredConfigs = filterLocSurchargeSurcharges(configs, locationId);

    String surchargeAllKey = getLocationSurchargeSurchargeALLCacheKey(locationId);

    List<String> allSurchargeKeysInCache = cacheService.getListValue(surchargeAllKey, String.class);
    cacheService.deleteByKeys(new HashSet<>(allSurchargeKeysInCache));
    cacheService.deleteByKey(surchargeAllKey);

    Map<String, Map<String, Map<String, Map<String, List<LocSurchargeEntity>>>>> mapConfig =
        SurchargeUtils.groupLocSurchargeConfigByIdProductChargeByDay(filteredConfigs);
    mapConfig.forEach((key, map) -> putLocSurchargeToCacheById(locationId, key, map));
  }

  @Override
  public void loadAllLocSurchargeConfigs(String locationId) {
    loadLocSurchargeConfigs(locationId);
    loadLocSurchargeAddresses(locationId);
  }

  private List<LocSurchargeAddressEntity> filterLocSurchargeAddresses(
      List<LocSurchargeAddressEntity> locAddresses, String locationId) {
    if (locationId == null || locationId.isEmpty()) {
      return locAddresses;
    }
    return locAddresses.stream().filter(x -> locationId.equals(x.getLocationId())).toList();
  }

  private List<LocSurchargeEntity> filterLocSurchargeSurcharges(
      List<LocSurchargeEntity> locSurchargeEntities, String locationId) {
    if (locationId == null || locationId.isEmpty()) {
      return locSurchargeEntities;
    }
    return locSurchargeEntities.stream().filter(x -> locationId.equals(x.getLocationId())).toList();
  }

  private void putAllLocSurchargeAddressesToCache(
      String locationId, Map<String, List<LocSurchargeAddressEntity>> mapConfig) {

    String cacheKeyAllAddress = getLocationSurchargeAddressALLCacheKey(locationId);
    List<String> allAddressesInCache = cacheService.getListValue(cacheKeyAllAddress, String.class);

    allAddressesInCache.forEach(
        address -> {
          String key =
              LocationSurchargeServiceImpl.LOC_SURCHARGE_ADDRESS_CACHE_PREFIX.concat(address);
          List<LocSurchargeAddressEntity> cachedEntities =
              cacheService.getListValue(key, LocSurchargeAddressEntity.class);
          List<LocSurchargeAddressEntity> filteredEntities =
              cachedEntities.stream()
                  .filter(entity -> !entity.getLocationId().equals(locationId))
                  .toList();

          cacheService.deleteByKey(key);
          if (!filteredEntities.isEmpty()) cacheService.setListValue(key, filteredEntities);
        });

    mapConfig.forEach(
        (mapKey, mapValue) -> {
          String cacheKey =
              LocationSurchargeServiceImpl.LOC_SURCHARGE_ADDRESS_CACHE_PREFIX.concat(mapKey);
          cacheService.setListValue(cacheKey, mapValue);
        });

    List<String> allAddresses = mapConfig.keySet().stream().toList();
    cacheService.deleteByKey(cacheKeyAllAddress);
    cacheService.setListValue(cacheKeyAllAddress, allAddresses);
  }

  private String getLocationSurchargeAddressALLCacheKey(String locationId) {

    String suffix = (locationId == null || locationId.isEmpty()) ? "ALL" : "ALL_" + locationId;
    return LocationSurchargeServiceImpl.LOC_SURCHARGE_ADDRESS_CACHE_PREFIX.concat(suffix);
  }

  private String getLocationSurchargeSurchargeALLCacheKey(String locationId) {

    String suffix = (locationId == null || locationId.isEmpty()) ? "ALL" : "ALL_" + locationId;
    return LocationSurchargeServiceImpl.LOC_SURCHARGE_SURCHARGE_CACHE_PREFIX.concat(suffix);
  }

  private void putLocSurchargeToCacheById(
      String locationId,
      String id,
      Map<String, Map<String, Map<String, List<LocSurchargeEntity>>>> mapConfig) {
    String prefix =
        LocationSurchargeServiceImpl.LOC_SURCHARGE_SURCHARGE_CACHE_PREFIX
            .concat(id)
            .concat(RedisKeyConstant.COLON);
    mapConfig.forEach(
        (mapKey, mapValue) ->
            putLocSurchargeToCacheByIdProduct(locationId, prefix, mapKey, mapValue));
  }

  private void putLocSurchargeToCacheByIdProduct(
      String locationId,
      String prefix,
      String product,
      Map<String, Map<String, List<LocSurchargeEntity>>> mapConfig) {
    String key = prefix.concat(product).concat(RedisKeyConstant.COLON);

    mapConfig.forEach(
        (mapKey, mapValue) ->
            putLocSurchargeToCacheByIdProductChargeBy(locationId, key, mapKey, mapValue));
  }

  private void putLocSurchargeToCacheByIdProductChargeBy(
      String locationId,
      String prefix,
      String chargeBy,
      Map<String, List<LocSurchargeEntity>> mapConfig) {
    String key = prefix.concat(chargeBy).concat(RedisKeyConstant.COLON);
    List<String> allSurchargeKeys = new ArrayList<>();

    mapConfig.forEach(
        (mapKey, mapValue) -> {
          String cacheKey = key.concat(mapKey);
          cacheService.setListValue(cacheKey, mapValue);
          allSurchargeKeys.add(cacheKey);
        });

    String surchargeAllCacheKey = getLocationSurchargeSurchargeALLCacheKey(locationId);
    cacheService.setListValue(surchargeAllCacheKey, allSurchargeKeys);
  }

  @Override
  public void loadAllLocationSurchargeConfigs() {
    log.info("[loadAllLocationSurchargeConfigs] Start loadAllLocationSurchargeConfigs!");

    Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap = new HashMap<>();

    int page = 0;
    LocationSurchargeConfigQueryResponse configs =
        locationSurchargeConfigRepository.getLocationSurchargeConfigs(page);
    log.info("[loadAllLocationSurchargeConfigs] Get location surcharge DB page {}", page);
    if (Objects.isNull(configs) || CollectionUtils.isEmpty(configs.getConfigs())) {
      log.info("[loadAllLocationSurchargeConfigs] Location surcharge configs in database is empty");
      return;
    }

    while (!configs.getConfigs().isEmpty()) {
      List<LocationSurchargeConfigEntity> listConfigs = configs.getConfigs();
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> lisConfigAfterGroup =
          SurchargeUtils.groupConfigByDayInWeekThenAddress(listConfigs);

      allConfigMap =
          SurchargeUtils.mergeGroupConfigByDayInWeekThenAddress(allConfigMap, lisConfigAfterGroup);
      log.info("[loadAllLocationSurchargeConfigs] Merge page {} to all config", page);

      page++;
      configs = locationSurchargeConfigRepository.getLocationSurchargeConfigs(page);
      log.info("[loadAllLocationSurchargeConfigs] Get location surcharge DB page {}", page);
    }

    pushAllConfigToCache(allConfigMap);

    log.info("[loadAllLocationSurchargeConfigs] End loadAllLocationSurchargeConfigs!");
  }

  private void pushAllConfigToCache(
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap) {
    long startTime = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.MON);
    long endTime = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload MON take {}", endTime - startTime);

    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.TUE);
    long startTime2 = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.WED);
    long endTime2 = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload WED take {}", endTime2 - startTime2);

    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.THU);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.FRI);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.SAT);
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.SUN);
    long startTime3 = System.currentTimeMillis();
    pushConfigToCacheByDay(allConfigMap, FlatfareConstants.HOL);
    long endTime3 = System.currentTimeMillis();
    log.info("[loadAllLocationSurchargeConfigs] reload HOL take {}", endTime3 - startTime3);
  }

  private void pushConfigToCacheByDay(
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> allConfigMap,
      String dayIndicator) {
    try {
      String keyPrefix = LOC_KEY_CACHE_PREFIX.concat(dayIndicator).concat(RedisKeyConstant.COLON);

      Map<String, List<LocationSurchargeConfigEntity>> configsByDay =
          allConfigMap.get(dayIndicator);

      deleteLocationSurchargeConfigsByDay(dayIndicator);

      configsByDay.forEach(
          (addressRef, configList) -> {
            String keyCache = keyPrefix + addressRef;
            cacheService.setListValue(keyCache, configList);
          });

    } catch (Exception e) {
      log.error("[loadAllLocationSurchargeConfigs] Error while push config to Cache", e);
    }
    log.info("[loadAllLocationSurchargeConfigs] Reload cache for {} successfuly", dayIndicator);
  }

  @Override
  public List<LocationSurchargeConfig> getLocationSurcharge(
      LocationSurchargeConfigRequest locationSurchargeConfigRequest) {

    // Validate input
    if (locationSurchargeConfigRequest == null) {
      throw new IllegalArgumentException("LocationSurchargeConfigRequest cannot be null");
    }

    final OffsetDateTime requestDateTimeSG =
        DateUtils.convertToSGTime(locationSurchargeConfigRequest.getRequestDate());

    // Extract and transform request data
    final LocalTime requestLocalTime = requestDateTimeSG.toLocalTime();
    final String dayInWeekReq = resolveDayOfWeek(requestDateTimeSG);
    final String productId = resolveProductId(locationSurchargeConfigRequest.getProductId());
    final String chargeByType = resolveChargeByType(locationSurchargeConfigRequest.getChargeBy());

    log.info(
        "[getLocationSurcharge] requestLocalTime: {}, dayInWeekReq: {}, productId: {}, chargeByType: {}",
        requestLocalTime,
        dayInWeekReq,
        productId,
        chargeByType);

    List<LocSurchargeAddressEntity> locSurchargeAddressEntities =
        Optional.ofNullable(getLocSurchargeAddress(locationSurchargeConfigRequest.getAddressRef()))
            .orElse(Collections.emptyList());

    List<LocationSurchargeConfig> result;
    try {
      result =
          locSurchargeAddressEntities.stream()
              .flatMap(
                  locSurchargeAddressEntity ->
                      processLocSurchargeAddressEntity(
                          locSurchargeAddressEntity,
                          productId,
                          chargeByType,
                          dayInWeekReq,
                          requestLocalTime))
              .toList();
    } catch (Exception e) {
      log.error("[getLocationSurcharge] Error while processing locSurchargeAddressEntities", e);
      throw new RuntimeException("Failed to process surcharge data", e);
    }
    return Optional.of(result).orElse(Collections.emptyList());
  }

  @Override
  public LocationSurchargeConfig getLocationSurchargeConfig(
      LocationSurchargeConfigRequest locationSurchargeConfigRequest) {
    Optional<LocationSurchargeConfig> locationSurchargeConfig;
    final Date requestDate = Date.from(locationSurchargeConfigRequest.getRequestDate().toInstant());
    final LocalTime requestDateTime = DateUtils.convertToLocalTime(requestDate);
    final String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(requestDate);
    final String addressRef = locationSurchargeConfigRequest.getAddressRef();
    final String chargeByRequest = locationSurchargeConfigRequest.getChargeBy();
    final String productId =
        "FLAT_001".equalsIgnoreCase(locationSurchargeConfigRequest.getProductId())
            ? FlatfareConstants.NORMAL_FLATFARE_PDT_ID
            : locationSurchargeConfigRequest.getProductId();
    final String chargeByType;

    chargeByType =
        FlatfareConstants.CHARGE_BY_PICKUP.equalsIgnoreCase(chargeByRequest)
            ? FlatfareConstants.CHARGE_BY_PICKUP
            : FlatfareConstants.CHARGE_BY_DEST;
    Stream<LocationSurchargeConfig> locationSurchargeConfigStream;

    final List<LocationSurchargeConfig> configs =
        getLocSurConfigsByDayAndAddr(dayInWeekReq, addressRef);
    final List<LocationSurchargeConfig> locSurchargeDayInWeekConfigs =
        configs.stream()
            .filter(
                locSur ->
                    (addressRef.equalsIgnoreCase(locSur.getAddressRef()))
                        && FlatfareConstants.LOC_SURC_TYPE.equalsIgnoreCase(locSur.getFareType())
                        && chargeByType.equalsIgnoreCase(locSur.getChargeBy())
                        && productId.equalsIgnoreCase(locSur.getProductId())
                        && (locSur.getStartTime().isBefore(requestDateTime)
                            && locSur.getEndTime().isAfter(requestDateTime)))
            .toList();

    if (DateUtils.isHoliday(requestDate, flatFareConfigService.getListHoliday())) {
      final List<LocationSurchargeConfig> configsHOL =
          getLocSurConfigsByDayAndAddr(FlatfareConstants.HOL, addressRef);

      final List<LocationSurchargeConfig> locSurchargeHolidayConfigs =
          configsHOL.stream()
              .filter(
                  locSur ->
                      (addressRef.equalsIgnoreCase(locSur.getAddressRef()))
                          && FlatfareConstants.LOC_SURC_TYPE.equalsIgnoreCase(locSur.getFareType())
                          && chargeByType.equalsIgnoreCase(locSur.getChargeBy())
                          && productId.equalsIgnoreCase(locSur.getProductId())
                          && (locSur.getStartTime().isBefore(requestDateTime)
                              && locSur.getEndTime().isAfter(requestDateTime)))
              .toList();
      locationSurchargeConfigStream =
          Stream.concat(locSurchargeHolidayConfigs.stream(), locSurchargeDayInWeekConfigs.stream());
      if (locSurchargeHolidayConfigs.isEmpty()) {
        locationSurchargeConfig =
            locationSurchargeConfigStream
                .filter(locSur -> locSur.getDayIndicator().contains(dayInWeekReq))
                .reduce((firstLocSur, lastLocSur) -> lastLocSur);
      } else {
        locationSurchargeConfig =
            locationSurchargeConfigStream
                .filter(
                    locSur ->
                        locSur.getDayIndicator().contains(FlatfareConstants.HOL)
                            || locSur.getDayIndicator().contains(dayInWeekReq))
                .findFirst();
      }
    } else {
      locationSurchargeConfig =
          locSurchargeDayInWeekConfigs.stream().reduce((firstLocSur, lastLocSur) -> lastLocSur);
    }
    if (locationSurchargeConfig.isPresent()) {
      return locationSurchargeConfig.get();
    } else {
      log.info("[getLocationSurchargeConfig] locationSurchargeConfig is empty");
      return null;
    }
  }

  private void deleteLocationSurchargeConfigsByDay(final String dayIndicator) {
    final String locSurchargeKeyPattern =
        LOC_KEY_CACHE_PREFIX
            .concat(dayIndicator)
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.WILDCARD);
    final Set<String> keys = cacheService.getKeysByPattern(locSurchargeKeyPattern);
    cacheService.deleteByKeys(keys);
  }

  @Override
  public List<LocationSurchargeConfig> getLocationSurchargeConfigList(
      final Date reqDate,
      final String pickupAddrRef,
      final String intermediateAddrRef,
      final String dropOffAddrRef,
      final List<FlatFareHoliday> holidayList) {
    final String dayInWeekReq = DateUtils.toDayOfWeekShortUpperCase(reqDate);

    final Stream<LocationSurchargeConfig> stream;

    final List<LocationSurchargeConfig> locSurchargeDayInWeekConfigs =
        getLocSurConfigsByDayAndAddr(
            dayInWeekReq, pickupAddrRef, intermediateAddrRef, dropOffAddrRef);

    if (DateUtils.isHolidaySingTime(reqDate, holidayList)) {
      final List<LocationSurchargeConfig> locSurchargeHolidayConfigs =
          getLocSurConfigsByDayAndAddr(
              FlatfareConstants.HOL, pickupAddrRef, intermediateAddrRef, dropOffAddrRef);
      stream =
          Stream.concat(locSurchargeHolidayConfigs.stream(), locSurchargeDayInWeekConfigs.stream());
    } else {
      stream = locSurchargeDayInWeekConfigs.stream();
    }
    return stream.toList();
  }

  private List<LocationSurchargeConfig> getLocSurConfigsByDayAndAddr(
      final String dayIndicator, final String... addressParts) {
    List<LocationSurchargeConfig> combinedConfigs = new ArrayList<>();
    if (ObjectUtils.isEmpty(addressParts)) {
      return combinedConfigs;
    }
    final String keyCache =
        RedisKeyConstant.DYNAMIC_PRICING
            .concat(RedisKeyConstant.COLON)
            .concat(RedisKeyConstant.LOC_SURC)
            .concat(RedisKeyConstant.COLON)
            .concat(dayIndicator)
            .concat(RedisKeyConstant.COLON);
    for (String addressPart : addressParts) {
      List<LocationSurchargeConfig> locSurConfigs =
          cacheService.getListValue(keyCache + addressPart, LocationSurchargeConfig.class);
      combinedConfigs.addAll(locSurConfigs);
    }

    return combinedConfigs;
  }

  private Stream<LocationSurchargeConfig> processLocSurchargeAddressEntity(
      LocSurchargeAddressEntity locSurchargeAddressEntity,
      String productId,
      String chargeByType,
      String dayInWeekReq,
      LocalTime requestLocalTime) {
    return getLocSurchargeById(
            locSurchargeAddressEntity.getLocationId(), productId, chargeByType, dayInWeekReq)
        .stream()
        .filter(resEntity -> resEntity.isValid(requestLocalTime))
        .map(
            locSurchargeEntity ->
                buildLocationSurchargeConfig(
                    locSurchargeEntity, locSurchargeAddressEntity.getAddressRef()));
  }

  private String resolveDayOfWeek(OffsetDateTime requestDateTimeSG) {
    return DateUtils.isHoliday(
            Date.from(requestDateTimeSG.toInstant()), flatFareConfigService.getListHoliday())
        ? FlatfareConstants.HOL
        : requestDateTimeSG
            .getDayOfWeek()
            .getDisplayName(TextStyle.SHORT, Locale.ENGLISH)
            .toUpperCase();
  }

  private String resolveProductId(String requestedProductId) {
    return "FLAT_001".equalsIgnoreCase(requestedProductId)
        ? FlatfareConstants.NORMAL_FLATFARE_PDT_ID
        : requestedProductId;
  }

  private String resolveChargeByType(String requestedChargeBy) {
    return FlatfareConstants.CHARGE_BY_PICKUP.equalsIgnoreCase(requestedChargeBy)
        ? FlatfareConstants.CHARGE_BY_PICKUP
        : FlatfareConstants.CHARGE_BY_DEST;
  }

  private LocationSurchargeConfig buildLocationSurchargeConfig(
      LocSurchargeEntity locSurchargeEntity, String addressRef) {
    return LocationSurchargeConfig.builder()
        .locationName(locSurchargeEntity.getLocationName())
        .locationId(Integer.parseInt(locSurchargeEntity.getLocationId()))
        .surchargeValue(locSurchargeEntity.getSurchargeValue())
        .chargeBy(locSurchargeEntity.getChargeBy())
        .fareType(locSurchargeEntity.getFareType())
        .endTime(locSurchargeEntity.getEndTime())
        .startTime(locSurchargeEntity.getStartTime())
        .dayIndicator(locSurchargeEntity.getDayIndicator())
        .addressRef(addressRef)
        .productId(locSurchargeEntity.getProductId())
        .build();
  }

  private List<LocSurchargeEntity> getLocSurchargeById(
      String id, String product, String chargeBy, String day) {
    final String keyCache =
        LOC_SURCHARGE_SURCHARGE_CACHE_PREFIX
            .concat(id)
            .concat(RedisKeyConstant.COLON)
            .concat(product)
            .concat(RedisKeyConstant.COLON)
            .concat(chargeBy)
            .concat(RedisKeyConstant.COLON)
            .concat(day);
    return cacheService.getListValue(keyCache, LocSurchargeEntity.class);
  }

  private List<LocSurchargeAddressEntity> getLocSurchargeAddress(String addressRef) {
    final String keyCache = LOC_SURCHARGE_ADDRESS_CACHE_PREFIX.concat(addressRef);

    return cacheService.getListValue(keyCache, LocSurchargeAddressEntity.class);
  }
}
