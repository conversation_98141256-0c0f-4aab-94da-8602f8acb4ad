package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public interface StaticTimeBasedConfigurationRepository {

  /**
   * Get all versions of static time-based configurations.
   *
   * @return a list of all versions
   */
  List<StaticBasedConfigurationVersionEntity> findAllVersions();

  StaticTimeBasedConfigurationEntity save(StaticTimeBasedConfigurationEntity entity);

  StaticTimeBasedConfigurationEntity update(StaticTimeBasedConfigurationEntity entity);

  List<StaticTimeBasedConfigurationEntity> findAllByVersion(String version);

  Optional<StaticTimeBasedConfigurationEntity> findById(Long id);

  List<StaticTimeBasedConfigurationEntity> findByEffectiveTimeRange(Instant effectiveTime);

  /**
   * Find a map of static time-based configurations by effective time range.
   *
   * <p>The result format is:
   *
   * <p>{name: {dayOfWeek: {hourOfDay: value}}}
   *
   * @param effectiveTime the time at which the configurations should be effective
   * @return a map of configurations
   */
  default Map<String, Map<DayOfWeekEnum, Map<Integer, String>>> findMapByEffectiveTimeRange(
      Instant effectiveTime) {
    return findByEffectiveTimeRange(effectiveTime).stream()
        .collect(
            Collectors.toMap(
                StaticTimeBasedConfigurationEntity::getName,
                entity ->
                    entity.getAppliedHours().stream()
                        .collect(
                            Collectors.groupingBy(
                                StaticTimeBasedConfigurationEntity.AppliedHour::getDayOfWeek,
                                Collectors.toMap(
                                    StaticTimeBasedConfigurationEntity.AppliedHour::getHourOfDay,
                                    StaticTimeBasedConfigurationEntity.AppliedHour::getValue,
                                    (oldValue, newValue) -> newValue))),
                (oldValue, newValue) -> newValue));
  }

  StaticBasedConfigurationEffectiveCheckEntity effectiveCheck();

  void deleteById(Long id);

  /**
   * Batch create static time-based configurations.
   *
   * @param configurations the list of configurations to create
   * @return the list of created configurations
   */
  List<StaticTimeBasedConfigurationEntity> batchCreateTimeRegionBasedConfigurations(
      List<StaticTimeBasedConfigurationEntity> configurations);
}
