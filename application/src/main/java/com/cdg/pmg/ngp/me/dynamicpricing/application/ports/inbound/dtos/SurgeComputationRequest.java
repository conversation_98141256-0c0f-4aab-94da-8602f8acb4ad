package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class SurgeComputationRequest {
  public static final String H3_REGION_ID_KEY = "h3_region_id";
  public static final String CURRENT_SURGE = "current_surge";
  public static final String GET_FARE_COUNT = "get_fare_count";

  private Long modelId;
  private String modelName;

  private String endpointUrl;
  private HttpRequest httpRequest;

  @Data
  public static class HttpRequest {
    private String request_id;
    private String model_name;
    private OffsetDateTime request_time;
    private List<Map<String, BigDecimal>> regions;
  }
}
