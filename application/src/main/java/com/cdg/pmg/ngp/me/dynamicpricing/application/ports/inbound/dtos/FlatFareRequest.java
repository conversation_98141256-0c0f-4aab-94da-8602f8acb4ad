package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FlatFareRequest implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String originAddressRef;
  private Double originAddressLat;
  private Double originAddressLng;
  private String originZoneId;
  private String destAddressRef;
  private Double destAddressLat;
  private Double destAddressLng;
  private String destZoneId;
  private String intermediateAddrRef;
  private Double intermediateAddrLat;
  private Double intermediateAddrLng;
  private String intermediateZoneId;
  private Integer vehTypeId;
  private String mobileId;
  private String countryCode;
  private String bookingChannel;
  private Date requestDate;
  private long routingDistance;
  private long ett;
  private boolean isDynamicPricing;
  private String jobType;
  private String encodedPolyline;
  private String tripId;
  private String fareId;
  private List<Integer> vehTypeIdList;
  private BigDecimal regionSurge;

  @Override
  public String toString() {
    return "FlatFareRequest={"
        + " jobType="
        + jobType
        + ", routingDistance="
        + routingDistance
        + ", ett="
        + ett
        + ", vehTypeId="
        + vehTypeId
        + "}";
  }
}
