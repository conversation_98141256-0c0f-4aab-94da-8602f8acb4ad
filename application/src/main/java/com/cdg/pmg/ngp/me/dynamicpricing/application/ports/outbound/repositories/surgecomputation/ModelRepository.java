package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import java.util.List;
import java.util.Optional;

public interface ModelRepository {
  ModelEntity save(ModelEntity entity);

  List<ModelEntity> findAll();

  Optional<ModelEntity> findById(Long id);

  void deleteById(Long id);

  List<Long> findIdByIds(List<Long> modelIds);

  List<String> findConfigNamesUsedByOtherModels(List<String> configNames, Long modelId);

  String findModelName(Long id);
}
