package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.ConfigurationContext;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class StaticTimeBasedConfigurationProvider implements ConfigurationDataProvider {
  /** The map format is: {name: {dayOfWeek: {hourOfDay: value}}} */
  private final Map<String, Map<DayOfWeekEnum, Map<Integer, String>>> staticTimeConfig;

  private final boolean isHoliday;

  @Override
  public BigDecimal getConfigurationData(final ConfigurationContext context) {
    if (staticTimeConfig == null) {
      log.warn(
          "[StaticTimeBasedConfigurationProvider] The static time based configuration is null");
      return null;
    }

    final String name = context.getName();

    Map<DayOfWeekEnum, Map<Integer, String>> weekHourMap = staticTimeConfig.get(name);
    if (weekHourMap == null) {
      log.warn(
          "[StaticTimeBasedConfigurationProvider] There is no static time based configuration for name: {}",
          name);
      return null;
    }

    ZonedDateTime zdt = context.getTime().atZone(ZoneId.systemDefault());

    DayOfWeekEnum dayOfWeekEnum;
    if (isHoliday) {
      dayOfWeekEnum = DayOfWeekEnum.PUBLIC_HOLIDAY;
    } else {
      int dayOfWeekNumber = zdt.getDayOfWeek().getValue();
      dayOfWeekEnum = DayOfWeekEnum.getByCode(dayOfWeekNumber);
    }

    Map<Integer, String> hourMap = weekHourMap.get(dayOfWeekEnum);
    if (hourMap == null) {
      log.warn(
          "[StaticTimeBasedConfigurationProvider] There is no static time based configuration for name: {}",
          name);
      return null;
    }

    int hourOfDay = zdt.getHour();
    BigDecimal value = CommonUtils.toBigDecimal(hourMap.get(hourOfDay));
    if (value == null) {
      log.warn(
          "[StaticTimeBasedConfigurationProvider] There is no static time based configuration for name: {}",
          name);
      return null;
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[StaticTimeBasedConfigurationProvider] Retrieved value, config name: {}, dayOfWeekEnum: {}, hourOfDay: {}, value: {}",
          name,
          dayOfWeekEnum,
          hourOfDay,
          value);
    }
    return value;
  }
}
