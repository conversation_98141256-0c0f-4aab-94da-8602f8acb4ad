package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.AdditionalChargeConfigItem;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AdditionalChargeDataMapper {

  private AdditionalChargeDataMapper() {}
  ;

  public static AdditionalChargeFeeData convertToAdditionalChargeFeeData(
      AdditionalChargeDriverFeeData additionalChargeDriverFeeData) {
    if (additionalChargeDriverFeeData == null) {
      return null;
    }

    AdditionalChargeFeeData.AdditionalChargeFeeDataBuilder additionalChargeFeeData =
        AdditionalChargeFeeData.builder();

    additionalChargeFeeData.chargeAmt(additionalChargeDriverFeeData.getTotalFareDriverFee());
    additionalChargeFeeData.chargeId(additionalChargeDriverFeeData.getChargeId());
    additionalChargeFeeData.chargeType(additionalChargeDriverFeeData.getChargeType());

    if (additionalChargeDriverFeeData.getChargeThreshold() != null) {
      additionalChargeFeeData.chargeThreshold(
          BigDecimal.valueOf(additionalChargeDriverFeeData.getChargeThreshold()));
    }
    if (additionalChargeDriverFeeData.getChargeUpperLimit() != null) {
      additionalChargeFeeData.chargeUpperLimit(
          BigDecimal.valueOf(additionalChargeDriverFeeData.getChargeUpperLimit()));
    }
    if (additionalChargeDriverFeeData.getChargeLowerLimit() != null) {
      additionalChargeFeeData.chargeLowerLimit(
          BigDecimal.valueOf(additionalChargeDriverFeeData.getChargeLowerLimit()));
    }

    return additionalChargeFeeData.build();
  }

  public static AdditionalChargeConfigData convertToAdditionalChargeConfigData(
      AdditionalChargeDriverFeeData additionalChargeDriverFeeData) {
    if (additionalChargeDriverFeeData == null) {
      return null;
    }
    AdditionalChargeConfigData.AdditionalChargeConfigDataBuilder additionalChargeConfigData =
        AdditionalChargeConfigData.builder();

    additionalChargeConfigData.chargeId(additionalChargeDriverFeeData.getChargeId());
    additionalChargeConfigData.chargeType(additionalChargeDriverFeeData.getChargeType());
    additionalChargeConfigData.chargeThreshold(additionalChargeDriverFeeData.getChargeThreshold());
    additionalChargeConfigData.chargeUpperLimit(
        additionalChargeDriverFeeData.getChargeUpperLimit());
    additionalChargeConfigData.chargeLowerLimit(
        additionalChargeDriverFeeData.getChargeLowerLimit());
    additionalChargeConfigData.isOptional(additionalChargeDriverFeeData.getIsOptional());
    additionalChargeConfigData.vehicleAttributeId(
        additionalChargeDriverFeeData.getVehicleAttributeId());
    return additionalChargeConfigData.build();
  }

  public static AdditionalChargeConfigItem convertToAdditionalChargeConfigItem(
      AdditionalChargeDriverFeeData additionalChargeDriverFeeData) {
    if (additionalChargeDriverFeeData == null) {
      return null;
    }
    AdditionalChargeConfigItem.AdditionalChargeConfigItemBuilder additionalChargeConfigItem =
        AdditionalChargeConfigItem.builder();

    additionalChargeConfigItem.chargeId(additionalChargeDriverFeeData.getChargeId());
    additionalChargeConfigItem.chargeType(additionalChargeDriverFeeData.getChargeType());
    additionalChargeConfigItem.chargeThreshold(additionalChargeDriverFeeData.getChargeThreshold());
    additionalChargeConfigItem.chargeUpperLimit(
        additionalChargeDriverFeeData.getChargeUpperLimit());
    additionalChargeConfigItem.chargeLowerLimit(
        additionalChargeDriverFeeData.getChargeLowerLimit());

    return additionalChargeConfigItem.build();
  }
}
