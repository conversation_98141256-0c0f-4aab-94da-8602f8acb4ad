package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.PARSING_HOLIDAY_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.BookARideConfigsConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/** Date Utils Service */
@Slf4j
public class DateUtils {
  public static final String DDMMYYYY_FORMAT = "dd-MM-yyyy";
  public static final String YYYYMMDD_HHMMSS_FORMAT = "yyyy-MM-dd hh:mm:ss";

  public static final String SING_ZONE_ID = "Asia/Singapore";

  private DateUtils() {}

  /**
   * Convert from String hhmmss to LocalTime
   *
   * @param typeHHmmss string hhmmss
   * @return LocalTime
   */
  public static LocalTime convertToLocalTime(String typeHHmmss) {
    LocalTime localTime = null;
    try {
      localTime = LocalTime.parse(typeHHmmss);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }

    return localTime;
  }

  /**
   * Convert from Date to LocalTime
   *
   * @param date date
   * @return LocalTime
   */
  public static LocalTime convertToLocalTime(Date date) {
    LocalTime localTime = null;
    try {
      ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
      localTime = zonedDateTime.toLocalTime();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return localTime;
  }

  /**
   * Check time between start and end
   *
   * @param candidate current time
   * @param start start time
   * @param end end time
   * @return boolean
   */
  public static boolean isBetween(LocalTime candidate, LocalTime start, LocalTime end) {
    if (start.isAfter(end)) {
      return !candidate.isBefore(start) || !candidate.isAfter(end);
    } else {
      return !candidate.isBefore(start) && !candidate.isAfter(end);
    }
  }

  /**
   * Check Date between start and end
   *
   * @param candidate current date
   * @param start start date
   * @param end end date
   * @return boolean
   */
  public static boolean isBetween(Date candidate, Date start, Date end) {
    return start.compareTo(candidate) <= 0 && candidate.compareTo(end) <= 0;
  }

  /**
   * Check Date between start and end
   *
   * @param candidate current date
   * @param start start date
   * @param end end date
   * @return boolean
   */
  public static boolean isBetween(Date candidate, LocalDate start, LocalDate end) {
    ZonedDateTime zonedDateTime = candidate.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
    LocalDate localDate = zonedDateTime.toLocalDate();
    return !localDate.isBefore(start) && !localDate.isAfter(end);
  }

  /**
   * Convert Date in UTC to day of week short upper case in Sing time
   *
   * @param date date
   * @return Mon, Tue, ....
   */
  public static String toDayOfWeekShortUpperCase(Date date) {
    String dayOfWeekInShort = StringUtils.EMPTY;
    if (Objects.nonNull(date)) {
      ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
      final DayOfWeek dayOfWeek = zonedDateTime.getDayOfWeek();
      dayOfWeekInShort = dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
    }
    return dayOfWeekInShort.toUpperCase();
  }

  /**
   * Get hour of date
   *
   * @param date date
   * @return hour 0 to 24
   */
  public static Integer getHourOfDate(Date date) {
    ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
    return zonedDateTime.getHour();
  }

  /**
   * Convert Date to String dd-MM-yyyy format
   *
   * @param date date
   * @return String in dd-MM-yyyy format
   */
  public static String toddMMyyyy(Date date) {
    return new SimpleDateFormat(DDMMYYYY_FORMAT).format(date);
  }

  /**
   * Convert String yyyy-MM-dd hh:mm:ss to date format
   *
   * @param date date in dd-MM-yyyy format
   * @return String in dd-MM-yyyy format
   */
  public static Date stringToDDmmYYYY(String date) {
    Date convertedDate = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      convertedDate = sdf.parse(date);
    } catch (Exception e) {
      log.error("[stringToDDmmYYYY] convert string to date format, invalid request date");
    }
    return convertedDate;
  }

  /**
   * Convert String yyyy-MM-dd hh:mm:ss to dd-MM-yyyy format
   *
   * @param daytime String yyyy-MM-dd hh:mm:ss
   * @return String in dd-MM-yyyy format
   */
  public static String toddMMyyyy(String daytime) throws ParseException {
    Date date = new SimpleDateFormat(YYYYMMDD_HHMMSS_FORMAT).parse(daytime);
    return toddMMyyyy(date);
  }

  /**
   * Get day of month
   *
   * @param date date
   * @return day of month 1 to 31
   */
  public static String toDayOfMonth(Date date) {
    ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
    int dayOfMonth = zonedDateTime.getDayOfMonth();
    return String.valueOf(dayOfMonth);
  }

  /**
   * Get month of year
   *
   * @param date date
   * @return month of year 1 to 12
   */
  public static String toMonth(Date date) {
    ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(SING_ZONE_ID));
    int month = zonedDateTime.getMonthValue();
    return String.valueOf(month);
  }

  /**
   * Convert minute to millisecond
   *
   * @param minutes minutes
   * @return millisecond
   */
  public static long minuteToMillisecond(int minutes) {
    return minutes * 60L * 1000L;
  }

  /**
   * Convert minute to second
   *
   * @param minutes minutes
   * @return second
   */
  public static long minuteToSecond(int minutes) {
    return minutes * 60L;
  }

  // not in use
  public static boolean isHoliday(Date requestDay, List<FlatFareHoliday> listHoliday) {
    return listHoliday.stream()
        .anyMatch(
            publicHoliday -> {
              try {
                return publicHoliday.isPublicHoliday(requestDay);
              } catch (ParseException e) {
                log.error("Error at isHoliday:", e);
                throw new InternalServerException(
                    PARSING_HOLIDAY_ERROR.getMessage(), PARSING_HOLIDAY_ERROR.getErrorCode());
              }
            });
  }

  /**
   * Check request day is holiday or not in Singapore Time
   *
   * @param requestDay day in UTC
   * @param listHoliday holiday config
   * @return boolean
   */
  public static boolean isHolidaySingTime(Date requestDay, List<FlatFareHoliday> listHoliday) {
    if (Objects.isNull(listHoliday)) {
      return Boolean.FALSE;
    }
    String requestDayString = convertToSingDateInDDMMYYYFormat(requestDay);

    return listHoliday.stream()
        .anyMatch(
            publicHoliday -> {
              try {
                return publicHoliday.isPublicHoliday(requestDayString);
              } catch (ParseException e) {
                log.error("Error at isHoliday:", e);
                throw new InternalServerException(
                    PARSING_HOLIDAY_ERROR.getMessage(), PARSING_HOLIDAY_ERROR.getErrorCode());
              }
            });
  }

  public static String convertToSingDateInDDMMYYYFormat(Date requestDay) {
    ZonedDateTime zonedDateTime = requestDay.toInstant().atZone(ZoneId.of(SING_ZONE_ID));

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DDMMYYYY_FORMAT);
    return zonedDateTime.format(formatter);
  }

  public static String convertToSingDateInDDMMYYYFormat(String requestDay, String currentFormat)
      throws ParseException {
    SimpleDateFormat sdf = new SimpleDateFormat(currentFormat);
    return convertToSingDateInDDMMYYYFormat(sdf.parse(requestDay));
  }

  /**
   * check if applicable day is a holiday
   *
   * @param applicableDays applicableDays
   * @return true/false
   */
  public static boolean isApplicableDaysContainsHOL(String applicableDays) {
    return !StringUtils.isEmpty(applicableDays)
        && applicableDays.contains(BookARideConfigsConstant.HOL);
  }

  public static List<FlatFareHoliday> mapToListFlatFareHoliday(List<String> holidays) {
    List<FlatFareHoliday> flatFareHolidays = new ArrayList<>();
    for (String holiday : holidays) {
      FlatFareHoliday flatFareHoliday = FlatFareHoliday.builder().date(holiday).build();
      flatFareHolidays.add(flatFareHoliday);
    }
    return flatFareHolidays;
  }

  public static OffsetDateTime convertFromDateToOffsetDateTime(final Date date) {
    return date.toInstant().atOffset(ZoneOffset.UTC);
  }

  public static LocalTime convertToLocalTime(OffsetDateTime date) {
    if (date == null) {
      throw new IllegalArgumentException("OffsetDateTime cannot be null");
    }

    try {
      return date.atZoneSameInstant(ZoneId.of(SING_ZONE_ID)).toLocalTime();
    } catch (DateTimeException e) {
      log.error("Failed to convert OffsetDateTime to LocalTime: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to convert date to local time", e);
    }
  }

  public static OffsetDateTime convertToSGTime(OffsetDateTime date) {
    return date.atZoneSameInstant(ZoneId.of(SING_ZONE_ID)).toOffsetDateTime();
  }
}
