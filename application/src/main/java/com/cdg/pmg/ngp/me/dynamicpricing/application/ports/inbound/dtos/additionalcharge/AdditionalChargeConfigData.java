package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** This bean only contains additional charge config data from ngp-me-fare-svc */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalChargeConfigData implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Integer chargeId;
  private String chargeType;
  private Double chargeThreshold;
  private Double chargeUpperLimit;
  private Double chargeLowerLimit;
  private Boolean isOptional;
  private Integer vehicleAttributeId;
}
