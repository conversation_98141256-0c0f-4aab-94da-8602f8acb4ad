package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.manager;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.DynamicPricingCompute;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl.DynamicPricingComputeImpl;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ServiceComponent
@Slf4j
@RequiredArgsConstructor
public class DynamicPricingRegionBasedManager {

  public void computeTotalFare(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {

    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);

    dynpComputeInf.calFlagDown(flatFareVO);
    dynpComputeInf.calTier1Fare(flatFareVO);
    dynpComputeInf.calTier2Fare(flatFareVO);
    dynpComputeInf.calWaitTimeFare(flatFareVO);
    dynpComputeInf.calHourlySurcharge(flatFareVO);
    dynpComputeInf.calLocationSurcharge(flatFareVO);
    dynpComputeInf.calBookingFee(flatFareVO);
    dynpComputeInf.calMultiDestSurcharge(flatFareVO);
    dynpComputeInf.calEventSurcharge(flatFareVO);

    var dpBaseFareForSurge =
        flatFareVO.getFlagDown()
            + flatFareVO.getTier1Fare()
            + flatFareVO.getTier2Fare()
            + flatFareVO.getWaitTimeFare()
            + flatFareVO.getHourlySurcharge()
            + flatFareVO.getBookingFee();
    flatFareVO.setDpBaseFareForSurge(dpBaseFareForSurge);

    FlatFareRequest flatFareRequest = flatFareVO.getFlatFareRequest();
    BigDecimal regionSurge = flatFareRequest.getRegionSurge();

    if (regionSurge == null) {
      log.warn(
          "[computeTotalFare] Region surge is null, will use default value 0 for mobile: {}",
          flatFareRequest.getMobileId());
      regionSurge = BigDecimal.ZERO;
    }

    final double surgeValue = regionSurge.doubleValue();
    flatFareVO.setDpSurgePercent(surgeValue);

    final double surgePercent = surgeValue / 100;
    final double dpAfterAddingSurge = flatFareVO.getDpBaseFareForSurge() * (1 + surgePercent);
    final double dpSurgeAmount = dpAfterAddingSurge - flatFareVO.getDpBaseFareForSurge();
    flatFareVO.setDpSurgeAmt(dpSurgeAmount);
    final double dpFareAfterSurge =
        dpAfterAddingSurge
            + flatFareVO.getTotalLocSurCharge()
            + flatFareVO.getTotalEventSurCharge();

    flatFareVO.setPricePerKm(surgePercent);
    flatFareVO.setDpFareAfterSurge(dpFareAfterSurge);
    flatFareVO.setCalculated(Boolean.TRUE);
  }

  public void validateDesurge(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {
    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);
    dynpComputeInf.updateAfterApplyingSurgeFare(flatFareVO);
  }

  public void setLimitMinMax(FlatFareVO flatFareVO, DynamicPricingConfigSet fareTypeConfigSet) {
    final DynamicPricingCompute dynpComputeInf = new DynamicPricingComputeImpl(fareTypeConfigSet);
    dynpComputeInf.setDynpMinMaxForTotalFare(flatFareVO);
  }
}
