package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALCULATE_BREAKDOWN_FARE_ERROR;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.CALC_LOC_SURCHARGE_DYNP_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EventSurgeAddressConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocSurcharge;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocSurchargeSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeAddressEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
@UtilityClass
public class SurchargeUtils {

  /**
   * Calculate Location Surcharges for Limo
   *
   * @param vo flat fare
   * @param locationSurchargeConfigList list of location surcharge config
   */
  public static void calLocSurchargesForLimo(
      FlatFareVO vo, List<LocationSurchargeConfig> locationSurchargeConfigList) {
    log.info(
        "Start calLocSurchargesForLimo with vehTypeId={}", vo.getFlatFareRequest().getVehTypeId());
    try {

      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

      List<com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig>
          relatedSurchargeConfigs =
              locationSurchargeConfigList.stream()
                  .filter(
                      p ->
                          (vo.getFlatFareRequest()
                                      .getOriginAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef())
                                  || vo.getFlatFareRequest()
                                      .getDestAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef()))
                              && DateUtils.isBetween(currentTime, p.getStartTime(), p.getEndTime()))
                  .toList();

      final LocSurchargeSet locSurchargeResult =
          computeLocationSurchages(vo, relatedSurchargeConfigs);

      if (locSurchargeResult.getPickupLocSurchargeValue() != null) {
        if (locSurchargeResult
            .getPickupLocSurchargeValue()
            .getLocationName()
            .equalsIgnoreCase(FlatfareConstants.CHANGI_AIRPORT)) {
          vo.setPdtId(FlatfareConstants.GUA_PDT_ID);
        }
        vo.getLocSurCharge().add(locSurchargeResult.getPickupLocSurchargeValue());
      }

      if (locSurchargeResult.getDestLocSurchargeValue() != null) {
        if (locSurchargeResult
            .getDestLocSurchargeValue()
            .getLocationName()
            .equalsIgnoreCase(FlatfareConstants.CHANGI_AIRPORT)) {
          vo.setPdtId(FlatfareConstants.GUD_PDT_ID);
        }
        vo.getLocSurCharge().add(locSurchargeResult.getDestLocSurchargeValue());
      }

      if (!FlatfareConstants.GUA_PDT_ID.equalsIgnoreCase(vo.getPdtId())) {
        final boolean destIsChangiAir =
            relatedSurchargeConfigs.stream()
                .anyMatch(
                    config ->
                        vo.getFlatFareRequest()
                                .getDestAddressRef()
                                .equalsIgnoreCase(config.getAddressRef())
                            && FlatfareConstants.CHANGI_AIRPORT.equalsIgnoreCase(
                                config.getLocationName()));
        if (destIsChangiAir) {
          vo.setPdtId(FlatfareConstants.GUD_PDT_ID);
        } else {
          vo.setPdtId(FlatfareConstants.OWT_PDT_ID);
        }
      }

    } catch (Exception e) {
      throw new DomainException(
          CALCULATE_BREAKDOWN_FARE_ERROR.getMessage(),
          CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode());
    }
  }

  /**
   * Calculate Location Surcharges for Standard
   *
   * @param vo flat fare
   * @param locationSurchargeConfigList list of location surcharge config
   */
  public void calLocSurchargesForStandard(
      FlatFareVO vo, List<LocationSurchargeConfig> locationSurchargeConfigList) {
    log.info(
        "Start calLocSurchargesForStandard with vehTypeId={}",
        vo.getFlatFareRequest().getVehTypeId());
    if (ObjectUtils.isEmpty(locationSurchargeConfigList)) {
      return;
    }
    try {
      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

      final List<
              com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                  .LocationSurchargeConfig>
          relatedSurchargeConfigs =
              locationSurchargeConfigList.stream()
                  .filter(
                      p ->
                          (vo.getFlatFareRequest()
                                      .getOriginAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef())
                                  || vo.getFlatFareRequest()
                                      .getDestAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef())
                                  || (vo.getFlatFareRequest().getIntermediateAddrRef() != null
                                      && vo.getFlatFareRequest()
                                          .getIntermediateAddrRef()
                                          .equalsIgnoreCase(p.getAddressRef())))
                              && DateUtils.isBetween(currentTime, p.getStartTime(), p.getEndTime())
                              && FlatfareConstants.NORMAL_FLATFARE_PDT_ID.equalsIgnoreCase(
                                  p.getProductId()))
                  .toList();

      final LocSurchargeSet locSurchargeResult =
          computeLocationSurchages(vo, relatedSurchargeConfigs);

      setLocSurchargeResult(vo, locSurchargeResult);

    } catch (Exception e) {
      throw new DomainException(
          CALC_LOC_SURCHARGE_DYNP_ERROR.getMessage(), CALC_LOC_SURCHARGE_DYNP_ERROR.getErrorCode());
    }
  }

  /**
   * Calculate Location Surcharges for Meter
   *
   * @param vo flat fare
   * @param locationSurchargeConfigList list of location surcharge config
   */
  public void calLocSurchargesForEstStandard(
      FlatFareVO vo, List<LocationSurchargeConfig> locationSurchargeConfigList) {
    log.info(
        "Start calLocSurchargesForEstStandard with vehTypeId={}",
        vo.getFlatFareRequest().getVehTypeId());
    if (ObjectUtils.isEmpty(locationSurchargeConfigList)) {
      return;
    }
    try {
      final Date reqDate = vo.getFlatFareRequest().getRequestDate();
      final LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);

      final List<
              com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                  .LocationSurchargeConfig>
          relatedSurchargeConfigs =
              locationSurchargeConfigList.stream()
                  .filter(
                      p ->
                          (vo.getFlatFareRequest()
                                      .getOriginAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef())
                                  || vo.getFlatFareRequest()
                                      .getDestAddressRef()
                                      .equalsIgnoreCase(p.getAddressRef())
                                  || (vo.getFlatFareRequest().getIntermediateAddrRef() != null
                                      && vo.getFlatFareRequest()
                                          .getIntermediateAddrRef()
                                          .equalsIgnoreCase(p.getAddressRef())))
                              && DateUtils.isBetween(currentTime, p.getStartTime(), p.getEndTime())
                              && FlatfareConstants.STD_PDT_ID.equalsIgnoreCase(p.getProductId()))
                  .toList();

      final LocSurchargeSet locSurchargeResult =
          computeLocationSurchages(vo, relatedSurchargeConfigs);

      setLocSurchargeResult(vo, locSurchargeResult);

    } catch (Exception e) {
      throw new DomainException(
          CALCULATE_BREAKDOWN_FARE_ERROR.getMessage(),
          CALCULATE_BREAKDOWN_FARE_ERROR.getErrorCode());
    }
  }

  private LocSurchargeSet computeLocationSurchages(
      final FlatFareVO vo,
      final List<
              com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                  .LocationSurchargeConfig>
          relatedSurchargeConfigs) {
    final LocSurcharge pickupLocSurchargeValue =
        getLocationSurchargeFromConfig(
            relatedSurchargeConfigs,
            FlatfareConstants.CHARGE_BY_PICKUP,
            vo.getFlatFareRequest().getOriginAddressRef());

    final LocSurcharge destLocSurchargeValue =
        getLocationSurchargeFromConfig(
            relatedSurchargeConfigs,
            FlatfareConstants.CHARGE_BY_DEST,
            vo.getFlatFareRequest().getDestAddressRef());

    LocSurcharge intermediateLocSurchargeValue = null;
    if (vo.getFlatFareRequest().getIntermediateAddrRef() != null) {
      intermediateLocSurchargeValue =
          getLocationSurchargeFromConfig(
              relatedSurchargeConfigs,
              FlatfareConstants.CHARGE_BY_DEST,
              vo.getFlatFareRequest().getIntermediateAddrRef());
    }
    return LocSurchargeSet.builder()
        .pickupLocSurchargeValue(pickupLocSurchargeValue)
        .destLocSurchargeValue(destLocSurchargeValue)
        .intermediateLocSurchargeValue(intermediateLocSurchargeValue)
        .build();
  }

  private LocSurcharge getLocationSurchargeFromConfig(
      final List<
              com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos
                  .LocationSurchargeConfig>
          relatedSurcharges,
      final String chargeBy,
      final String addressReq) {
    LocSurcharge locSurcharge = null;

    List<com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig>
        surcharges =
            relatedSurcharges.stream()
                .filter(
                    p ->
                        chargeBy.equalsIgnoreCase(p.getChargeBy())
                            && addressReq.equalsIgnoreCase(p.getAddressRef()))
                .toList();

    for (com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig
        flatfareLocSurcharge : surcharges) {
      if (flatfareLocSurcharge.getDayIndicator() != null
          && flatfareLocSurcharge.getDayIndicator().equalsIgnoreCase(FlatfareConstants.HOL)) {
        locSurcharge =
            LocSurcharge.builder()
                .locSurchargeId(flatfareLocSurcharge.getLocationId())
                .chargeType(flatfareLocSurcharge.getFareType())
                .chargeType(flatfareLocSurcharge.getChargeBy())
                .amount(flatfareLocSurcharge.getSurchargeValue())
                .locationName(flatfareLocSurcharge.getLocationName())
                .build();
        break;
      } else {
        locSurcharge =
            LocSurcharge.builder()
                .locSurchargeId(flatfareLocSurcharge.getLocationId())
                .chargeType(flatfareLocSurcharge.getFareType())
                .chargeType(flatfareLocSurcharge.getChargeBy())
                .amount(flatfareLocSurcharge.getSurchargeValue())
                .locationName(flatfareLocSurcharge.getLocationName())
                .build();
      }
    }

    return locSurcharge;
  }

  private void setLocSurchargeResult(
      final FlatFareVO vo, final LocSurchargeSet locSurchargeResult) {
    if (locSurchargeResult.getPickupLocSurchargeValue() != null) {
      vo.getLocSurCharge().add(locSurchargeResult.getPickupLocSurchargeValue());
    }
    if (locSurchargeResult.getDestLocSurchargeValue() != null) {
      vo.getLocSurCharge().add(locSurchargeResult.getDestLocSurchargeValue());
    }
    if (locSurchargeResult.getIntermediateLocSurchargeValue() != null) {
      vo.getLocSurCharge().add(locSurchargeResult.getIntermediateLocSurchargeValue());
    }
  }

  public static boolean isEventConfigMatchingDayAndMonthAndTimeEffective(
      final EventSurgeAddressConfig configItem, final Date reqDate) {
    final String dayOfMonth = DateUtils.toDayOfMonth(reqDate);
    final String monthOfYear = DateUtils.toMonth(reqDate);
    final LocalTime reqTime = DateUtils.convertToLocalTime(reqDate);
    return configItem
            .getApplicableDaysOfMonth()
            .contains(CommonUtils.COMMA_DELIMITER + dayOfMonth + CommonUtils.COMMA_DELIMITER)
        && configItem
            .getApplicableMonths()
            .contains(CommonUtils.COMMA_DELIMITER + monthOfYear + CommonUtils.COMMA_DELIMITER)
        && DateUtils.isBetween(reqTime, configItem.getStartTime(), configItem.getEndTime());
  }

  public static boolean isAddressRefMatchingWithConfigUsingFixAmountAndChangeByPickUp(
      final String addressRef, final EventSurgeAddressConfig configItem) {
    return configItem
            .getApplicableAddresses()
            .contains(CommonUtils.COMMA_DELIMITER + addressRef + CommonUtils.COMMA_DELIMITER)
        && FlatfareConstants.CHARGE_BY_PICKUP.equalsIgnoreCase(configItem.getChargeBy())
        && FlatfareConstants.FIXED_AMOUNT.equalsIgnoreCase(configItem.getChargeType());
  }

  public static boolean isAddressRefMatchingWithConfigUsingPercentageAndChangeByPickUp(
      final String addressRef, final EventSurgeAddressConfig configItem) {
    return configItem
            .getApplicableAddresses()
            .contains(CommonUtils.COMMA_DELIMITER + addressRef + CommonUtils.COMMA_DELIMITER)
        && FlatfareConstants.CHARGE_BY_PICKUP.equalsIgnoreCase(configItem.getChargeBy())
        && FlatfareConstants.PERCENTAGE.equalsIgnoreCase(configItem.getChargeType());
  }

  public static boolean isAddressRefMatchingWithConfigUsingFixAmountAndChangeByDestination(
      final String addressRef, final EventSurgeAddressConfig configItem) {
    return configItem
            .getApplicableAddresses()
            .contains(CommonUtils.COMMA_DELIMITER + addressRef + CommonUtils.COMMA_DELIMITER)
        && FlatfareConstants.CHARGE_BY_DEST.equalsIgnoreCase(configItem.getChargeBy())
        && FlatfareConstants.FIXED_AMOUNT.equalsIgnoreCase(configItem.getChargeType());
  }

  public static boolean isAddressRefMatchingWithConfigUsingPercentageAndChangeByDestination(
      final String addressRef, final EventSurgeAddressConfig configItem) {
    return configItem
            .getApplicableAddresses()
            .contains(CommonUtils.COMMA_DELIMITER + addressRef + CommonUtils.COMMA_DELIMITER)
        && FlatfareConstants.CHARGE_BY_DEST.equalsIgnoreCase(configItem.getChargeBy())
        && FlatfareConstants.PERCENTAGE.equalsIgnoreCase(configItem.getChargeType());
  }

  public static Map<String, Map<String, List<LocationSurchargeConfigEntity>>>
      groupConfigByDayInWeekThenAddress(List<LocationSurchargeConfigEntity> cbdConfigs) {
    if (ObjectUtils.isEmpty(cbdConfigs)) {
      return new HashMap<>();
    }
    return cbdConfigs.stream()
        .collect(
            Collectors.groupingBy(
                LocationSurchargeConfigEntity::getDayIndicator,
                Collectors.groupingBy(LocationSurchargeConfigEntity::getAddressRef)));
  }

  public static Map<String, List<LocSurchargeAddressEntity>> groupLocSurchargeAddress(
      List<LocSurchargeAddressEntity> locSurchargeAddressEntities) {
    if (ObjectUtils.isEmpty(locSurchargeAddressEntities)) {
      return new HashMap<>();
    }

    return locSurchargeAddressEntities.stream()
        .collect(Collectors.groupingBy(LocSurchargeAddressEntity::getAddressRef));
  }

  public static Map<String, Map<String, Map<String, Map<String, List<LocSurchargeEntity>>>>>
      groupLocSurchargeConfigByIdProductChargeByDay(List<LocSurchargeEntity> inputConfig) {
    if (ObjectUtils.isEmpty(inputConfig)) {
      return new HashMap<>();
    }
    return inputConfig.stream()
        .collect(
            Collectors.groupingBy(
                LocSurchargeEntity::getLocationId,
                Collectors.groupingBy(
                    LocSurchargeEntity::getProductId,
                    Collectors.groupingBy(
                        LocSurchargeEntity::getChargeBy,
                        Collectors.groupingBy(LocSurchargeEntity::getDayIndicator)))));
  }

  public static Map<String, Map<String, List<LocationSurchargeConfigEntity>>>
      mergeGroupConfigByDayInWeekThenAddress(
          Map<String, Map<String, List<LocationSurchargeConfigEntity>>> map1,
          Map<String, Map<String, List<LocationSurchargeConfigEntity>>> map2) {
    try {
      Map<String, Map<String, List<LocationSurchargeConfigEntity>>> result = new HashMap<>(map1);

      map2.forEach(
          (outerKey, innerMap) ->
              innerMap.forEach(
                  (innerKey, list) ->
                      result
                          .computeIfAbsent(outerKey, k -> new HashMap<>())
                          .merge(
                              innerKey,
                              list,
                              (list1, list2) -> {
                                List<LocationSurchargeConfigEntity> mergedList =
                                    new ArrayList<>(list1);
                                mergedList.addAll(list2);
                                return mergedList.stream()
                                    .distinct() // Ensure no duplicates
                                    .toList();
                              })));
      return result;

    } catch (Exception e) {
      log.error("mergeGroupConfigByDayInWeekThenAddress error", e);
      return new HashMap<>();
    }
  }
}
