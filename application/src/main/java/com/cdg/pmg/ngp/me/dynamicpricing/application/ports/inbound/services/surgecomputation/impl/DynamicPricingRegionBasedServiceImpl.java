package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.ROUTE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.AdditionalChargeDataMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.AdditionalChargeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.DynpDomainMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl.FlatFareManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.DynamicPricingRegionBasedService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.manager.DynamicPricingRegionBasedManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.RouteUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.AdditionalChargeConfigItem;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.FareBreakdownDetailEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.MultiFareRequestEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.BookingChannelEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.JobTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class DynamicPricingRegionBasedServiceImpl implements DynamicPricingRegionBasedService {

  private static final String APPLICABLE_TYPE_YES = "YES";
  private static final String APPLICABLE_TYPE_NO = "NO";
  private static final String APPLICABLE_TYPE_WAIVE = "WAIVE";
  private static final String DRIVER_FEE = "DRIVER_FEE";

  private final AddressService addressService;
  private final CacheService cacheService;
  private final LocationSurchargeService locationSurchargeService;
  private final FareService fareService;
  private final FlatFareManager flatFareManager;
  private final DynpDomainMapper domainMapper;
  private final DynamicPricingRegionBasedManager dynamicPricingManager;
  private final DriverFeeAdditionalChargeProcessor driverFeeAdditionalChargeProcessor;
  private final AdditionalChargeMapper additionalChargeMapper;
  private final SurgeFactorCalculationService surgeFactorCalculationService;

  @Override
  public MultiFareResponse getMultiFare(final MultiFareRequestQuery requestQuery) {
    MultiFareRequestEntity multiFareRequestEntity =
        domainMapper.mapToEstFareRequestEntity(requestQuery);
    validateCalcMultiFareRequest(multiFareRequestEntity);

    FarePricingContext context = preparePricingContext(multiFareRequestEntity);

    List<FlatFareVOPart> flatFareVOPartList = calculateAllVehicleTypeFares(context);

    if (flatFareVOPartList.isEmpty()) {
      throw new BadRequestException(
          NO_VEH_TYPE_ID_AVAILABLE.getMessage(), NO_VEH_TYPE_ID_AVAILABLE.getErrorCode());
    }

    return buildAndCacheResponse(context, flatFareVOPartList);
  }

  private FarePricingContext preparePricingContext(MultiFareRequestEntity multiFareRequestEntity) {
    Date requestTime = determineRequestTime(multiFareRequestEntity);
    RouteInfo routeInfo = getRouteInfo(requestTime, multiFareRequestEntity);
    String fareId = CommonUtils.generateFareId(requestTime, multiFareRequestEntity.getMobile());
    BigDecimal regionSurge = getRegionSurge(multiFareRequestEntity);
    FlatFareRequest flatFareRequest =
        createFlatFareRequest(multiFareRequestEntity, routeInfo, requestTime, fareId, regionSurge);

    ConfigurationSet configSet = loadConfigurations(flatFareRequest);

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        loadAdditionalChargeConfig();

    int timeExpireMultiFare =
        flatFareManager.getCacheTimerMultiFlatFareInMinute(
            configSet.getCommonConfigSet().getCacheTimerMinsMultiFlatFare());

    return FarePricingContext.builder()
        .multiFareRequestEntity(multiFareRequestEntity)
        .flatFareRequest(flatFareRequest)
        .routeInfo(routeInfo)
        .fareId(fareId)
        .requestTime(requestTime)
        .configSet(configSet)
        .additionalChargeFeeConfigMap(additionalChargeFeeConfigMap)
        .timeExpireMultiFare(timeExpireMultiFare)
        .build();
  }

  private BigDecimal getRegionSurge(final MultiFareRequestEntity multiFareRequestEntity) {
    Map<Long, BigDecimal> h3RegionSurgeMap =
        surgeFactorCalculationService.getH3RegionSurgeMap(multiFareRequestEntity.getModelName());
    BigDecimal regionSurge = h3RegionSurgeMap.get(multiFareRequestEntity.getRegionId());
    if (log.isDebugEnabled()) {
      log.debug(
          "[getRegionSurge] Region surge: {}, mobile: {}, pickupAddressRef: {}, pickupAddressLat: {}, pickupAddressLng: {}, regionId: {}, modelId: {}, modelName: {}",
          regionSurge,
          multiFareRequestEntity.getMobile(),
          multiFareRequestEntity.getPickupAddressRef(),
          multiFareRequestEntity.getPickupAddressLat(),
          multiFareRequestEntity.getPickupAddressLng(),
          multiFareRequestEntity.getRegionId(),
          multiFareRequestEntity.getModelId(),
          multiFareRequestEntity.getModelName());
    }
    return regionSurge;
  }

  private ConfigurationSet loadConfigurations(FlatFareRequest flatFareRequest) {
    CommonConfigSet commonConfigSet =
        cacheService.getValue(CACHE_KEY_COMMON_CONFIG_SET, CommonConfigSet.class);
    FlatFareConfigSet limoConfigSet =
        cacheService.getValue(CACHE_KEY_LIMO_CONFIG_SET, FlatFareConfigSet.class);
    FlatFareConfigSet estStandardConfigSet =
        cacheService.getValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, FlatFareConfigSet.class);
    DynamicPricingConfigSet fareTypeConfigSet =
        cacheService.getValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, DynamicPricingConfigSet.class);

    fareTypeConfigSet.setMultiStopSurcharge(limoConfigSet.getMultiStopSurcharge());
    fareTypeConfigSet.setEventSurgeAddressConfigList(
        limoConfigSet.getEventSurgeAddressConfigList());

    // Get Holiday Config
    List<FlatFareHoliday> holidayList =
        cacheService.getListValue(DYNAMIC_PRICING + COLON + COMPANY_HOLIDAY, FlatFareHoliday.class);
    limoConfigSet.setHolidayList(holidayList);
    estStandardConfigSet.setHolidayList(holidayList);
    fareTypeConfigSet.setHolidayList(holidayList);

    // GET LOC SURCHARGE CONFIG
    final List<LocationSurchargeConfig> locationSurchargeConfigList =
        getLocationSurchargeConfigList(flatFareRequest, holidayList);
    limoConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);
    estStandardConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);
    fareTypeConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);

    // Get Booking Fee List
    final List<BookingFeeItem> bookingFeeList =
        getBookingFeeList(flatFareRequest, commonConfigSet, limoConfigSet);
    limoConfigSet.setBookingFeeList(bookingFeeList);
    estStandardConfigSet.setBookingFeeList(bookingFeeList);

    // Get Platform Fee Config
    final List<PlatformFeeResponse> platformFeeConfigList =
        getPlatformFeeConfigList(flatFareRequest, commonConfigSet);

    return ConfigurationSet.builder()
        .commonConfigSet(commonConfigSet)
        .limoConfigSet(limoConfigSet)
        .estStandardConfigSet(estStandardConfigSet)
        .fareTypeConfigSet(fareTypeConfigSet)
        .platformFeeConfigList(platformFeeConfigList)
        .build();
  }

  private List<LocationSurchargeConfig> getLocationSurchargeConfigList(
      final FlatFareRequest flatFareRequest, final List<FlatFareHoliday> holidayList) {
    return locationSurchargeService.getLocationSurchargeConfigList(
        flatFareRequest.getRequestDate(),
        flatFareRequest.getOriginAddressRef(),
        flatFareRequest.getIntermediateAddrRef(),
        flatFareRequest.getDestAddressRef(),
        holidayList);
  }

  private List<PlatformFeeResponse> getPlatformFeeConfigList(
      final FlatFareRequest flatFareRequest, final CommonConfigSet commonConfigSet) {
    final PlatformFeeListRequest platformFeeListRequest =
        flatFareManager.createPlatformFeeListRequest(flatFareRequest, commonConfigSet);
    return fareService.getPlatformFeeByList(platformFeeListRequest);
  }

  private List<BookingFeeItem> getBookingFeeList(
      final FlatFareRequest flatFareRequest,
      final CommonConfigSet commonConfigSet,
      final FlatFareConfigSet limoConfigSet) {
    final BookingFeeListRequest bookingFeeListRequest =
        flatFareManager.createBookingFeeListRequest(
            flatFareRequest, commonConfigSet, limoConfigSet.getHolidayList());
    final BookingFeeListResponse bookingFeeListResponse =
        fareService.getBookingFeeByList(bookingFeeListRequest);
    return bookingFeeListResponse.getBookingFeeList();
  }

  private Map<String, List<AdditionalChargeFeeConfigResponse>> loadAdditionalChargeConfig() {
    try {
      return fareService.getAdditionalChargeFeeConfigMap(null, null, null);
    } catch (Exception e) {
      log.error("Get additional charge fee config from fare-svc failed.", e);
      return new HashMap<>();
    }
  }

  private List<FlatFareVOPart> calculateAllVehicleTypeFares(FarePricingContext context) {
    List<FlatFareVOPart> flatFareVOPartList = new ArrayList<>();
    MultiFareRequestEntity requestEntity = context.getMultiFareRequestEntity();

    if (JobTypeEnum.isAdvance(requestEntity.getJobType())) {
      List<Integer> advanceVehIdList =
          flatFareManager.filterAdvanceVehId(
              requestEntity.getVehTypeIDs(), context.getConfigSet().getCommonConfigSet());
      requestEntity.setVehTypeIDs(advanceVehIdList);
    }

    for (int vehTypeId : requestEntity.getVehTypeIDs()) {
      try {
        List<FlatFareVOPart> flatFarePart = calculateSingleVehicleTypeFare(context, vehTypeId);
        flatFareVOPartList.addAll(flatFarePart);
      } catch (Exception exception) {
        log.error(
            "Unable to calculate vehTypeId={}, exception={}", vehTypeId, exception.getMessage());
      }
    }

    return flatFareVOPartList;
  }

  private List<FlatFareVOPart> calculateSingleVehicleTypeFare(
      FarePricingContext context, int vehTypeId) {
    FlatFareRequest flatFareRequest = context.getFlatFareRequest();
    flatFareRequest.setVehTypeId(vehTypeId);

    ConfigurationSet configSet = context.getConfigSet();
    EstimatedFare estimatedFare = computeEstimatedFare(flatFareRequest, configSet);
    FlatFareVO finalFlatFare = combineEstimatedFlatFare(estimatedFare);

    FareCalculationParams fareParams = buildFareCalculationParams(context, estimatedFare);

    List<FlatFareVOPart> fareParts = buildFareParts(finalFlatFare, fareParams, context);

    cacheFareBreakdown(finalFlatFare, fareParts, context.getTimeExpireMultiFare());

    return fareParts;
  }

  private FareCalculationParams buildFareCalculationParams(
      FarePricingContext context, EstimatedFare estimatedFare) {
    MultiFareRequestEntity requestEntity = context.getMultiFareRequestEntity();
    boolean isNeedCalculateDriverFee = isNeedCalculateDriverFee(requestEntity);
    boolean isDynamicPrice = estimatedFare.getDynamicPricingForStandard().isCalculated();

    CommonConfigSet commonConfigSet = context.getConfigSet().getCommonConfigSet();
    int surgeLvl =
        flatFareManager.computeSurgeLevel(
            isDynamicPrice,
            estimatedFare.getDynamicPricingForStandard(),
            commonConfigSet.getDriverSurgeLevelIndications());
    int surgeIndicator =
        flatFareManager.computeSurgeIndicator(
            isDynamicPrice,
            estimatedFare.getDynamicPricingForStandard(),
            commonConfigSet.getSurgeIndicatorThreshold(),
            commonConfigSet.getSurgeIndicatorThresholdZero());

    return FareCalculationParams.builder()
        .isNeedCalculateDriverFee(isNeedCalculateDriverFee)
        .isDynamicPrice(isDynamicPrice)
        .surgeLvl(surgeLvl)
        .surgeIndicator(surgeIndicator)
        .build();
  }

  private List<FlatFareVOPart> buildFareParts(
      FlatFareVO finalFlatFare, FareCalculationParams fareParams, FarePricingContext context) {
    List<FlatFareVOPart> fareParts = new ArrayList<>();
    int vehTypeId = finalFlatFare.getFlatFareRequest().getVehTypeId();
    CommonConfigSet commonConfigSet = context.getConfigSet().getCommonConfigSet();
    FlatFareRequest flatFareRequest = context.getFlatFareRequest();

    if (shouldShowFlatFare(vehTypeId, flatFareRequest.getJobType(), commonConfigSet)) {
      FlatFareVOPart flatFarePart = buildFlatFarePart(finalFlatFare, fareParams, context);
      fareParts.add(flatFarePart);
    }

    if (shouldShowMeterFare(vehTypeId, commonConfigSet)) {
      FlatFareVOPart meterFarePart = buildMeterFarePart(finalFlatFare, fareParams, context);
      fareParts.add(meterFarePart);
    }

    return fareParts;
  }

  private boolean shouldShowFlatFare(
      int vehTypeId, String jobType, CommonConfigSet commonConfigSet) {
    return !flatFareManager.isShowMeterFareOnly(vehTypeId, commonConfigSet)
        && !JobTypeEnum.isAdvance(jobType);
  }

  private boolean shouldShowMeterFare(int vehTypeId, CommonConfigSet commonConfigSet) {
    return !flatFareManager.isShowFlatFareOnly(vehTypeId, commonConfigSet);
  }

  private FlatFareVOPart buildFlatFarePart(
      FlatFareVO finalFlatFare, FareCalculationParams fareParams, FarePricingContext context) {
    boolean isNormalFlatFare =
        FlatfareConstants.NORMAL_FLATFARE_PDT_ID.equalsIgnoreCase(finalFlatFare.getPdtId());

    FlatFareVOPart flatFarePart =
        FlatFareVOPart.builder()
            .vehTypeId(finalFlatFare.getFlatFareRequest().getVehTypeId())
            .totalFare(finalFlatFare.getTotalFare())
            .estimatedFareLF(finalFlatFare.getTotalFare())
            .estimatedFareRT(finalFlatFare.getTotalFare())
            .pdtId(finalFlatFare.getPdtId())
            .drvSurgeLvl(isNormalFlatFare ? fareParams.getSurgeLvl() : 0)
            .paxSurgeIndicator(isNormalFlatFare ? fareParams.getSurgeIndicator() : 0)
            .build();

    calculateAndSetAdditionalCharges(flatFarePart, context, fareParams.isNeedCalculateDriverFee());

    calculateAndSetPlatformFee(flatFarePart, finalFlatFare.getFlatFareRequest(), context);

    return flatFarePart;
  }

  private FlatFareVOPart buildMeterFarePart(
      FlatFareVO finalFlatFare, FareCalculationParams fareParams, FarePricingContext context) {
    FlatFareVOPart meterFarePart =
        FlatFareVOPart.builder()
            .vehTypeId(finalFlatFare.getFlatFareRequest().getVehTypeId())
            .totalFare(finalFlatFare.getTotalFare())
            .estimatedFareLF(finalFlatFare.getEstimatedFareLF())
            .estimatedFareRT(finalFlatFare.getEstimatedFareRT())
            .pdtId(FlatfareConstants.STD_PDT_ID)
            .drvSurgeLvl(0)
            .paxSurgeIndicator(0)
            .build();

    calculateAndSetAdditionalCharges(meterFarePart, context, fareParams.isNeedCalculateDriverFee());

    calculateAndSetPlatformFee(meterFarePart, finalFlatFare.getFlatFareRequest(), context);

    return meterFarePart;
  }

  private void calculateAndSetAdditionalCharges(
      FlatFareVOPart farePart, FarePricingContext context, boolean isNeedCalculateDriverFee) {
    /*
      Driver Fee requirement ,jira ticket - https://comfortdelgrotaxi.atlassian.net/browse/NGPME-9207
      Calculate driver fee ,then add driver to totalFare,estimatedFareLF,estimatedFareRT
    */
    List<AdditionalChargeDriverFeeData> additionalChargeDriverFeeDataList =
        driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
            farePart,
            context.getAdditionalChargeFeeConfigMap(),
            isNeedCalculateDriverFee,
            context.getMultiFareRequestEntity().getBookingChannel());

    List<AdditionalChargeConfigData> additionalCharges = new ArrayList<>();

    additionalChargeDriverFeeDataList.forEach(
        additionalChargeDriverFeeData -> {
          /*
            Set additionalChargeDriverFeeData to the return flatFarePart or meterFarePart,
            will store to cache along with flatFarePart or meterFarePart for api /v1.0/pricing/additional-charge-fees use
          */
          if (DRIVER_FEE.equals(additionalChargeDriverFeeData.getChargeType())) {
            farePart.setAdditionalChargeDriverFeeData(additionalChargeDriverFeeData);
          }

          // Collect additional charge config data
          additionalCharges.add(
              AdditionalChargeDataMapper.convertToAdditionalChargeConfigData(
                  additionalChargeDriverFeeData));
        });

    // Set additional charge config data list to the return value "flatFarePart" or "meterFarePart"
    farePart.setAdditionalCharges(additionalCharges);
  }

  private void calculateAndSetPlatformFee(
      FlatFareVOPart farePart, FlatFareRequest flatFareRequest, FarePricingContext context) {
    PlatformFee platformFee =
        computePlatformFee(
            farePart, flatFareRequest, context.getConfigSet().getPlatformFeeConfigList());
    farePart.setPlatformFeeConfigId(platformFee.getConfigId());
    farePart.setPlatformFeeLower(platformFee.getPlatformFeeLower());
    farePart.setPlatformFeeUpper(platformFee.getPlatformFeeUpper());
  }

  private MultiFareResponse buildAndCacheResponse(
      FarePricingContext context, List<FlatFareVOPart> flatFareVOPartList) {
    Date calculatedDate = new Date();

    MultiFareResponse multiFareResponse =
        createEstimatedFareResponse(context, flatFareVOPartList, calculatedDate);

    storeMultiFareToCache(context.getFareId(), multiFareResponse);
    storeRouteInfoToCache(context.getRouteInfo(), context.getTimeExpireMultiFare());

    return multiFareResponse;
  }

  private PlatformFee computePlatformFee(
      final FlatFareVOPart flatFareVO,
      final FlatFareRequest flatFareRequest,
      final List<PlatformFeeResponse> platformFeeConfigList) {
    final PlatformFee platformFee = new PlatformFee();

    if (!ObjectUtils.isEmpty(platformFeeConfigList)) {
      final Date currentDate = flatFareRequest.getRequestDate();
      final List<PlatformFeeResponse> configAfterFilter =
          platformFeeConfigList.stream()
              .filter(
                  config ->
                      flatFareRequest
                              .getBookingChannel()
                              .equalsIgnoreCase(config.getBookingChannel())
                          && flatFareVO.getPdtId().equalsIgnoreCase(config.getProductId())
                          && flatFareRequest.getVehTypeId().equals(config.getVehicleGroupId())
                          && !config.getDeleted()
                          && isPlatformFeeEffective(
                              currentDate, config.getEffectiveFrom(), config.getEffectiveTo()))
              .toList();

      if (ObjectUtils.isNotEmpty(configAfterFilter) && Objects.nonNull(configAfterFilter.get(0))) {
        final PlatformFeeResponse finalConfig = configAfterFilter.get(0);
        platformFee.setConfigId(finalConfig.getId());
        switch (finalConfig.getPlatformFeeApplicability()) {
          case APPLICABLE_TYPE_NO, APPLICABLE_TYPE_WAIVE -> setPlatformFeeZero(platformFee);
          case APPLICABLE_TYPE_YES -> setPlatformFeeLowerAndHigher(
              flatFareVO, platformFee, finalConfig, flatFareRequest);
          default -> log.error("Error to get ApplicableType of platform fee response");
        }
      }
    }
    return platformFee;
  }

  private void setPlatformFeeZero(final PlatformFee platformFee) {
    platformFee.setPlatformFeeLower(FlatfareConstants.PLATFORM_FEE_DEFAULT);
    platformFee.setPlatformFeeUpper(FlatfareConstants.PLATFORM_FEE_DEFAULT);
  }

  private void setPlatformFeeLowerAndHigher(
      final FlatFareVOPart flatFareVO,
      final PlatformFee platformFee,
      final PlatformFeeResponse configResponse,
      final FlatFareRequest flatFareRequest) {
    final double fareLF = flatFareVO.getEstimatedFareLF().doubleValue();
    final double fareRT = flatFareVO.getEstimatedFareRT().doubleValue();
    final Double threshold = configResponse.getPlatformFeeThresholdLimit();
    final double totalFare = flatFareVO.getTotalFare().doubleValue();

    if (FlatfareConstants.STD_PDT_ID.equalsIgnoreCase(flatFareVO.getPdtId())) {
      if (JobTypeEnum.IMMEDIATE.getValue().equalsIgnoreCase(flatFareRequest.getJobType())) {
        // NGPME-9582: change to use medianFare for meter
        final double medianFare = (fareLF + fareRT) / 2;
        final double platformFeeMeter =
            medianFare < threshold
                ? configResponse.getLowerPlatformFee()
                : configResponse.getUpperPlatformFee();
        platformFee.setPlatformFeeLower(platformFeeMeter);
        platformFee.setPlatformFeeUpper(platformFeeMeter);
      } else if (JobTypeEnum.ADVANCE.getValue().equalsIgnoreCase(flatFareRequest.getJobType())) {
        // NGPME-9789: update AJ lower price to upper price
        platformFee.setPlatformFeeLower(configResponse.getUpperPlatformFee());
        platformFee.setPlatformFeeUpper(configResponse.getUpperPlatformFee());
      }
    } else {
      platformFee.setPlatformFeeLower(
          totalFare < threshold
              ? configResponse.getLowerPlatformFee()
              : configResponse.getUpperPlatformFee());
      platformFee.setPlatformFeeUpper(platformFee.getPlatformFeeLower());
    }
  }

  private boolean isPlatformFeeEffective(
      final Date currentDate, final String effectiveFrom, final String effectiveTo) {
    try {
      final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      final Date startDate = simpleDateFormat.parse(effectiveFrom);
      final Date endDate = simpleDateFormat.parse(effectiveTo);
      return DateUtils.isBetween(currentDate, startDate, endDate);
    } catch (ParseException exception) {
      log.error(exception.getMessage(), exception);
    }
    return false;
  }

  /**
   * Below situation will not need to calculate driver fee: 1. jobType is STREET 2. jobType is not
   * STREET,but booking channel is IVR,CSA or SMS
   *
   * @param multiFareRequestEntity use jobType and booking channel to judge
   * @return true - calculate driver fee ; false - not calculate driver fee
   */
  private boolean isNeedCalculateDriverFee(MultiFareRequestEntity multiFareRequestEntity) {
    // If the job type is STREET,then not need to calculate driver fee
    if (JobTypeEnum.isStreet(multiFareRequestEntity.getJobType())) {
      return false;
    } else {
      return BookingChannelEnum.isNeedCalculateDriverFee(
          multiFareRequestEntity.getBookingChannel());
    }
  }

  private EstimatedFare computeEstimatedFare(
      final FlatFareRequest flatFareRequest, final ConfigurationSet configSet) {
    final int vehTypeIdReq = flatFareRequest.getVehTypeId();
    log.info("Compute Estimated Fare with vehTypeId={}", vehTypeIdReq);

    final CommonConfigSet commonConfigSet = configSet.getCommonConfigSet();

    if (!flatFareManager.isValidVehTypeId(vehTypeIdReq, commonConfigSet)) {
      throw new DomainException(
          INVALID_VEH_TYPE_ID.getMessage(), INVALID_VEH_TYPE_ID.getErrorCode());
    }

    FlatFareVO flatAndMeterFareForLimo = new FlatFareVO();
    FlatFareVO meterFareForStandard = new FlatFareVO();
    FlatFareVO dynamicPricingForStandard = new FlatFareVO();

    if (flatFareManager.isFlatLimoType(vehTypeIdReq, commonConfigSet)
        || flatFareManager.isEstLimoType(vehTypeIdReq, commonConfigSet)) {
      flatAndMeterFareForLimo =
          flatFareManager.computeTotalFare(configSet.getLimoConfigSet(), flatFareRequest);
    } else {
      if (flatFareManager.isEstFareType(vehTypeIdReq, commonConfigSet)) {
        meterFareForStandard =
            flatFareManager.computeTotalFare(configSet.getEstStandardConfigSet(), flatFareRequest);
      }
      if (flatFareManager.isDynamicFareType(vehTypeIdReq, commonConfigSet)) {
        dynamicPricingForStandard =
            computeDynamicPricing(
                meterFareForStandard.getMeteredBaseFare(),
                meterFareForStandard.getEstimatedFareLF(),
                meterFareForStandard.getEstimatedFareRT(),
                configSet.getFareTypeConfigSet(),
                flatFareRequest);
      }
    }

    log.info(
        "[Calc Multi Fare] {}, flatAndMeterFareForLimo={}, meterFareForStandard={}, dynamicPricingForStandard={}",
        flatFareRequest,
        flatAndMeterFareForLimo.isCalculated() ? flatAndMeterFareForLimo : "null",
        meterFareForStandard.isCalculated() ? meterFareForStandard : "null",
        dynamicPricingForStandard.isCalculated() ? dynamicPricingForStandard : "null");

    return EstimatedFare.builder()
        .flatAndMeterFareForLimo(flatAndMeterFareForLimo)
        .meterFareForStandard(meterFareForStandard)
        .dynamicPricingForStandard(dynamicPricingForStandard)
        .build();
  }

  private void cacheFareBreakdown(
      final FlatFareVO flatFareVO,
      final List<FlatFareVOPart> fareParts,
      final int timeExpireMultiFare) {

    FlatFareVOPart flatFarePart = null;
    FlatFareVOPart meterFarePart = null;
    for (final FlatFareVOPart farePart : fareParts) {
      if (FlatfareConstants.STD_PDT_ID.equalsIgnoreCase(farePart.getPdtId())) {
        meterFarePart = farePart;
      } else {
        flatFarePart = farePart;
      }
    }

    storeFareBreakdownToCache(flatFareVO, flatFarePart, meterFarePart, timeExpireMultiFare);
  }

  private FlatFareVO computeDynamicPricing(
      final double meteredBaseFare,
      final BigDecimal estimatedFareLF,
      final BigDecimal estimatedFareRT,
      final DynamicPricingConfigSet fareTypeConfigSet,
      final FlatFareRequest flatFareRequest) {
    final FlatFareVO dynamicPricing = new FlatFareVO();
    dynamicPricing.setFlatFareRequest(flatFareRequest);
    dynamicPricing.setMeteredBaseFare(meteredBaseFare);
    dynamicPricing.setEstimatedFareLF(estimatedFareLF);
    dynamicPricing.setEstimatedFareRT(estimatedFareRT);
    dynamicPricingManager.computeTotalFare(dynamicPricing, fareTypeConfigSet);
    dynamicPricingManager.validateDesurge(dynamicPricing, fareTypeConfigSet);
    dynamicPricingManager.setLimitMinMax(dynamicPricing, fareTypeConfigSet);
    return dynamicPricing;
  }

  private FlatFareVO combineEstimatedFlatFare(final EstimatedFare estimatedFare) {
    final FlatFareVO finalFlatFare;
    final boolean isLimoCalculated = estimatedFare.getFlatAndMeterFareForLimo().isCalculated();
    final boolean isDynamicPriceCalculated =
        estimatedFare.getDynamicPricingForStandard().isCalculated();

    if (isLimoCalculated) {
      finalFlatFare = estimatedFare.getFlatAndMeterFareForLimo();
    } else {
      if (isDynamicPriceCalculated) {
        finalFlatFare = estimatedFare.getDynamicPricingForStandard();
        finalFlatFare.setPdtId(FlatfareConstants.NORMAL_FLATFARE_PDT_ID);
      } else {
        finalFlatFare = estimatedFare.getMeterFareForStandard();
      }
    }
    return finalFlatFare;
  }

  private MultiFareResponse createEstimatedFareResponse(
      FarePricingContext context,
      final List<FlatFareVOPart> flatFareVOPartList,
      Date calculatedDate) {
    final FlatFareRequest flatFareRequest = context.getFlatFareRequest();
    return MultiFareResponse.builder()
        .fareId(context.getFareId())
        .pickupAddressRef(flatFareRequest.getOriginAddressRef())
        .destAddressRef(flatFareRequest.getDestAddressRef())
        .intermediateAddrRef(flatFareRequest.getIntermediateAddrRef())
        .mobile(flatFareRequest.getMobileId())
        .countryCode(flatFareRequest.getCountryCode())
        .fareCalcTime(flatFareRequest.getRequestDate().toInstant().atOffset(ZoneOffset.UTC))
        .estimatedTripTime(flatFareRequest.getEtt())
        .distance(flatFareRequest.getRoutingDistance())
        .encodedPolyline(flatFareRequest.getEncodedPolyline())
        .tripId(flatFareRequest.getTripId())
        .fareExpiredIn(context.getTimeExpireMultiFare())
        .calculatedDate(calculatedDate)
        .flatFareVOParts(flatFareVOPartList)
        .build();
  }

  private void storeFareBreakdownToCache(
      final FlatFareVO flatFareVO,
      final FlatFareVOPart flatFarePart,
      final FlatFareVOPart meterFarePart,
      final int timeExpireBreakDown) {

    final FlatFareRequest flatFareRequest = flatFareVO.getFlatFareRequest();
    final long flatFarePlatformFeeId;
    final double flatFarePlatformFee;
    final long meterPlatformFeeId;
    final double meterPlatformFeeLower;
    final double meterPlatformFeeUpper;
    List<AdditionalChargeConfigItem> additionalCharges = new ArrayList<>();

    if (flatFarePart == null) {
      flatFarePlatformFeeId = FlatfareConstants.PLATFORM_FEE_ID_DEFAULT;
      flatFarePlatformFee = FlatfareConstants.PRICE_DEFAULT;
    } else {
      flatFarePlatformFeeId = flatFarePart.getPlatformFeeConfigId();
      flatFarePlatformFee = flatFarePart.getPlatformFeeUpper();
      additionalCharges =
          additionalChargeMapper.mapToAdditionalChargeConfigItems(
              flatFarePart.getAdditionalCharges());
    }

    if (meterFarePart == null) {
      meterPlatformFeeId = FlatfareConstants.PLATFORM_FEE_ID_DEFAULT;
      meterPlatformFeeLower = FlatfareConstants.PRICE_DEFAULT;
      meterPlatformFeeUpper = FlatfareConstants.PRICE_DEFAULT;
    } else {
      meterPlatformFeeId = meterFarePart.getPlatformFeeConfigId();
      meterPlatformFeeLower = meterFarePart.getPlatformFeeLower();
      meterPlatformFeeUpper = meterFarePart.getPlatformFeeUpper();
    }

    var streamFlatFareBreakdown =
        FareBreakdownDetailEntity.builder()
            .fareId(flatFareRequest.getFareId())
            .tripId(flatFareRequest.getTripId())
            .routingDistance(flatFareRequest.getRoutingDistance())
            .ett(flatFareRequest.getEtt())
            .encodedPolyline(flatFareRequest.getEncodedPolyline())
            .calMethod(FlatfareConstants.LIVE_TRAFFIC_DYNAMIC_PRICE)
            .pickupAddressRef(flatFareRequest.getOriginAddressRef())
            .pickupAddressLat(flatFareRequest.getOriginAddressLat())
            .pickupAddressLng(flatFareRequest.getOriginAddressLng())
            .pickupZoneId(flatFareRequest.getOriginZoneId())
            .destAddressRef(flatFareRequest.getDestAddressRef())
            .destAddressLat(flatFareRequest.getDestAddressLat())
            .destAddressLng(flatFareRequest.getDestAddressLng())
            .destZoneId(flatFareRequest.getDestZoneId())
            .requestDate(new Timestamp(flatFareRequest.getRequestDate().getTime()))
            .flagDownRate(flatFareVO.getFlagDown())
            .tier1Fare(flatFareVO.getTier1Fare())
            .tier2Fare(flatFareVO.getTier2Fare())
            .waitTimeFare(flatFareVO.getWaitTimeFare())
            .peakHrFare(flatFareVO.getPeakHrFare())
            .midNightFare(flatFareVO.getMidNightFare())
            .hourlySurcharge(flatFareVO.getHourlySurcharge())
            .bookingFee(flatFareVO.getBookingFee())
            .locSurcharge(flatFareVO.getTotalLocSurCharge())
            .eventSurcharge(flatFareVO.getTotalEventSurCharge())
            .additionalSurcharge(flatFareVO.getAdditionalSurcharge())
            .multiDestSurcharge(flatFareVO.getMultiDestSurcharge())
            .dpSurgePercent(flatFareVO.getDpSurgePercent())
            .dpSurgeAmount(flatFareVO.getDpSurgeAmt())
            .dpAppliedSurgeAmount(flatFareVO.getDpAplydSurgeAmt())
            .dpBaseFareForSurge(flatFareVO.getDpBaseFareForSurge())
            .dpFinalFare(flatFareVO.getDpFinalFare())
            .dpBatchKey(flatFareVO.getBatchKey())
            .meteredBaseFare(flatFareVO.getMeteredBaseFare())
            .totalFare(flatFareVO.getTotalFare())
            .estimatedFareLF(flatFareVO.getEstimatedFareLF())
            .estimatedFareRT(flatFareVO.getEstimatedFareRT())
            .flatPlatformFeeId(flatFarePlatformFeeId)
            .flatPlatformFee(flatFarePlatformFee)
            .meterPlatformFeeId(meterPlatformFeeId)
            .meterPlatformFeeLower(meterPlatformFeeLower)
            .meterPlatformFeeUpper(meterPlatformFeeUpper)
            .additionalCharges(additionalCharges);
    if (flatFareRequest.getIntermediateAddrRef() != null) {
      streamFlatFareBreakdown
          .intermediateAddrRef(flatFareRequest.getIntermediateAddrRef())
          .intermediateAddrLat(flatFareRequest.getIntermediateAddrLat())
          .intermediateAddrLng(flatFareRequest.getIntermediateAddrLng())
          .intermediateZoneId(flatFareRequest.getIntermediateZoneId());
    }

    final FareBreakdownDetailEntity fareBreakdown = streamFlatFareBreakdown.build();

    final String keyBreakdown =
        createFareBreakdownKey(flatFareRequest.getFareId(), flatFareRequest.getVehTypeId());

    log.info("KEY_BREAKDOWN={}", keyBreakdown);

    cacheService.setValue(
        keyBreakdown, fareBreakdown, DateUtils.minuteToSecond(timeExpireBreakDown));
  }

  private void storeMultiFareToCache(
      final String fareId, final MultiFareResponse multiFareResponse) {
    final String keyCache = CommonUtils.generateMultiFareCacheKey(fareId);
    log.info("KEY_MULTI_FARE={},VALUE_MULTI_FARE={}", keyCache, multiFareResponse);
    cacheService.setValue(
        keyCache,
        multiFareResponse,
        DateUtils.minuteToSecond(multiFareResponse.getFareExpiredIn()));
  }

  private void storeRouteInfoToCache(final RouteInfo routeInfo, int timeExpire) {
    final String keyCache = DYNAMIC_PRICING + COLON + ROUTE + COLON + routeInfo.getTripId();
    log.info("KEY_ROUTE_INFO={}", keyCache);
    cacheService.setValue(keyCache, routeInfo, DateUtils.minuteToSecond(timeExpire));
  }

  private FlatFareRequest createFlatFareRequest(
      final MultiFareRequestEntity estFareRequest,
      final RouteInfo route,
      final Date requestTime,
      final String fareId,
      final BigDecimal regionSurge) {
    return FlatFareRequest.builder()
        .fareId(fareId)
        .tripId(route.getTripId())
        .countryCode(estFareRequest.getCountryCode())
        .mobileId(estFareRequest.getMobile())
        .jobType(estFareRequest.getJobType())
        .bookingChannel(estFareRequest.getBookingChannel())
        .originAddressRef(estFareRequest.getPickupAddressRef())
        .originAddressLat(estFareRequest.getPickupAddressLat())
        .originAddressLng(estFareRequest.getPickupAddressLng())
        .originZoneId(estFareRequest.getPickupZoneId())
        .destAddressRef(estFareRequest.getDestAddressRef())
        .destAddressLat(estFareRequest.getDestAddressLat())
        .destAddressLng(estFareRequest.getDestAddressLng())
        .destZoneId(estFareRequest.getDestZoneId())
        .intermediateAddrRef(estFareRequest.getIntermediateAddrRef())
        .intermediateAddrLat(estFareRequest.getIntermediateAddrLat())
        .intermediateAddrLng(estFareRequest.getIntermediateAddrLng())
        .intermediateZoneId(estFareRequest.getIntermediateZoneId())
        .requestDate(requestTime)
        .routingDistance(route.getRoutingDistance())
        .ett(route.getEtt())
        .encodedPolyline(route.getEncodedPolyline())
        .vehTypeIdList(estFareRequest.getVehTypeIDs())
        .regionSurge(regionSurge)
        .build();
  }

  private RouteInfo getRouteInfo(
      final Date requestTime, final MultiFareRequestEntity multiFareRequestEntity) {
    final String tripId = CommonUtils.generateTripId(requestTime);
    final GenerateRouteRequest generateRouteRequest =
        RouteUtils.createGenerateRouteRequest(multiFareRequestEntity, tripId);
    final GenerateRouteResponse route = addressService.getRoute(generateRouteRequest);
    if (RouteUtils.isRouteResponseEmpty(route) || RouteUtils.isRouteResponseEttDistInvalid(route)) {
      log.error("Get route from Address Service empty or invalid");
      throw new BadRequestException(INVALID_ROUTE.getMessage(), INVALID_ROUTE.getErrorCode());
    }

    return RouteUtils.createRouteInfo(multiFareRequestEntity, route, tripId);
  }

  private Date determineRequestTime(MultiFareRequestEntity multiFareRequest) {
    OffsetDateTime fareDate = multiFareRequest.getFareDate();
    if (fareDate != null && JobTypeEnum.isAdvance(multiFareRequest.getJobType())) {
      return Date.from(fareDate.toInstant());
    }
    return new Date();
  }

  /**
   * Validates the input {@link MultiFareRequestEntity} for calculating multi-fare requests.
   *
   * <p>Performs the following validations:
   *
   * <ul>
   *   <li>Checks if the booking channel in the request is a valid enumeration value of {@link
   *       BookingChannelEnum}. Throws a {@link BadRequestException} if invalid.
   *   <li>Checks if the job type in the request is a valid enumeration value of {@link
   *       JobTypeEnum}. Throws a {@link BadRequestException} if invalid.
   * </ul>
   *
   * @param request the {@link MultiFareRequestEntity} containing fare request details.
   * @throws BadRequestException if:
   *     <ul>
   *       <li>The booking channel is invalid.
   *       <li>The job type is invalid.
   *     </ul>
   */
  private void validateCalcMultiFareRequest(final MultiFareRequestEntity request) {
    if (!EnumUtils.isValidEnum(BookingChannelEnum.class, request.getBookingChannel())) {
      throw new BadRequestException(
          INVALID_BOOKING_CHANNEL.getMessage(), INVALID_BOOKING_CHANNEL.getErrorCode());
    }
    if (!EnumUtils.isValidEnum(JobTypeEnum.class, request.getJobType())) {
      throw new BadRequestException(INVALID_JOB_TYPE.getMessage(), INVALID_JOB_TYPE.getErrorCode());
    }
  }

  private String createFareBreakdownKey(String fareId, int vehTypeId) {
    return DYNAMIC_PRICING
        + COLON
        + BREAKDOWN
        + COLON
        + fareId
        + FlatfareConstants.HYPHEN
        + vehTypeId;
  }

  @lombok.Builder
  @lombok.Data
  private static class FarePricingContext {
    private MultiFareRequestEntity multiFareRequestEntity;
    private FlatFareRequest flatFareRequest;
    private RouteInfo routeInfo;
    private String fareId;
    private Date requestTime;
    private ConfigurationSet configSet;
    private Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap;
    private int timeExpireMultiFare;
  }

  @lombok.Builder
  @lombok.Data
  private static class ConfigurationSet {
    private CommonConfigSet commonConfigSet;
    private FlatFareConfigSet limoConfigSet;
    private FlatFareConfigSet estStandardConfigSet;
    private DynamicPricingConfigSet fareTypeConfigSet;
    private List<PlatformFeeResponse> platformFeeConfigList;
  }

  @lombok.Builder
  @lombok.Data
  private static class FareCalculationParams {
    private boolean isNeedCalculateDriverFee;
    private boolean isDynamicPrice;
    private int surgeLvl;
    private int surgeIndicator;
  }
}
