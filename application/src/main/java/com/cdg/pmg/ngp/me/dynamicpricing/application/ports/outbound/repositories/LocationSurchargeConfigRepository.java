package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeAddressEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import java.util.List;

public interface LocationSurchargeConfigRepository {
  /**
   * Gets all location surcharge configuration.
   *
   * @param page search page
   * @return LocationSurchargeConfigQueryResponse
   */
  LocationSurchargeConfigQueryResponse getLocationSurchargeConfigs(int page);

  List<LocSurchargeEntity> getLocSurchargeConfigs();

  List<LocSurchargeAddressEntity> getLocSurchargeAddresses();
}
