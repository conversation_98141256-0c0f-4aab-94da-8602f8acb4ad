package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.PARSING_HOLIDAY_ERROR;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeCalculationDto;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicSurgeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.SurgeCalculationStrategy;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.CompanyHolidayRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynamicSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.DynpSurgeLogsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.PricingRangeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DemandSupplyConfigV2;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.PricingRangeCalDemandSurgeQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Implementation of {@link DynamicSurgeService} for managing dynamic surge pricing calculations and
 * updates. This service uses demand-supply data, pricing configurations, and holiday-related inputs
 * to determine appropriate surge levels for various zones.
 *
 * <p>Core functionalities provided by this service include:
 *
 * <ul>
 *   <li>Integration with fleet analytics to fetch demand-supply statistics
 *   <li>Validation and preparation of pricing range configurations
 *   <li>Efficient handling of surge-related data, including caching and database interactions
 *   <li>Support for newer surge calculation strategies (e.g., V2, V2.5, V3)
 *   <li>Logging of dynamically updated surge configurations for traceability
 * </ul>
 *
 * <p>This class is annotated with {@link ServiceComponent}, indicating it is a service-level
 * component within the application, and provides reusable business logic related to dynamic
 * pricing.
 *
 * <p>Using Lombok annotations such as {@link AllArgsConstructor} for constructor generation and
 * {@link Slf4j} for logging support, the class ensures a clean and efficient implementation.
 */
@ServiceComponent
@Slf4j
@AllArgsConstructor
public class DynSurgeEnhancementServiceImpl implements DynamicSurgeService {

  private final SurgeCalculationV1Strategy surgeCalculationV1Strategy;
  private final SurgeCalculationV2Strategy surgeCalculationV2Strategy;
  private final SurgeCalculationV3Strategy surgeCalculationV3Strategy;
  private final SurgeCalculationV2Point5Strategy surgeCalculationV2Point5Strategy;
  private final CompanyHolidayRepository companyHolidayRepository;
  private final CacheService cacheService;
  private final FleetAnalyticService fleetAnalyticService;
  private final PricingRangeRepository pricingRangeRepository;
  private final DynamicSurgeRepository dynamicSurgeRepository;
  private final DynpSurgeLogsRepository dynpSurgeLogsRepository;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;

  /**
   * Calculates the demand-supply surge and updates pricing configurations dynamically.
   *
   * <p>Steps performed in this method:
   *
   * <ul>
   *   <li>Fetch demand-supply statistics from the {@link FleetAnalyticService}.
   *   <li>Filter and validate demand-supply data and pricing range configurations.
   *   <li>Determine if the current day is a holiday, which affects surge calculations.
   *   <li>Remove outdated and invalid surge entries from the repository.
   *   <li>Join demand-supply data with pricing configurations to prepare for surge updates.
   *   <li>Calculate and update surge values for respective zones using configured strategies.
   *   <li>Store the updated surge configurations back into the repository.
   *   <li>Refresh the cache to ensure availability of the latest data for downstream consumers.
   *   <li>Insert logs for all updated surge configurations for traceability.
   * </ul>
   *
   * <p>This method is designed to be idempotent, ensuring that a batch of data is not processed
   * more than once based on its batch key.
   */
  @Override
  public void calculateDemandSupplySurge() {
    // Step 1: Log the start of the process
    log.info("Start calculating demand supply surge!");

    // Step 2: Fetch demand-supply statistics from Fleet Analytics Service
    var demandSupplyInfos =
        fleetAnalyticService.getDemandSupplyStatisticsV2(); // checked ok, this list is not null
    if (CollectionUtils.isEmpty(demandSupplyInfos)) {
      log.info("Demand supply infos from fleet analytics is empty!");
      return;
    }

    // Step 3: Fetch valid pricing model configurations grouped by zone Id
    var zoneIdToNewPricingModelConfigMap = getValidNewPricingConfigurationsGroupByZoneId();
    if (zoneIdToNewPricingModelConfigMap == null || zoneIdToNewPricingModelConfigMap.isEmpty()) {
      log.warn("No valid pricing model configurations found in zoneIdToNewPricingModelConfigMap");
      return;
    }
    log.info(
        "Found {} valid pricing model configurations", zoneIdToNewPricingModelConfigMap.size());

    // Step 4: Extract batch counter and existing dynamic surges
    var batchCounter = demandSupplyInfos.get(0).getBatchCounter();
    var dynpSurges = getDynpSurges();
    if (CollectionUtils.isEmpty(dynpSurges)) {
      log.info("existing dynpSurges is empty! Will create new entities.");
    } else {
      // Step 5: Check if the current batch has already been processed
      if (Objects.equals(batchCounter, dynpSurges.get(0).getBatchKey())) {
        log.info("The batch {} already run", batchCounter);
        return; // Exit if batch has already been processed
      }
    }

    // Step 6: Log the batch counter for tracking
    log.info("Demand Supply Batch counter = {}", batchCounter);

    // Step 7: Determine if the current day is a holiday
    var isHoliday = getIsHolidayValueConfig();
    log.info("IsHoliday: {}", isHoliday);

    // Step 8: Remove invalid dynamic surge entries from the repository
    log.info("Remove invalid dyn surge started");
    dynamicSurgeRepository.removeInvalidDynSurges();
    log.info("Removed invalid dyn surge completed");

    // Step 9: Fetch pricing range configurations for demand surge
    var pricingRangeConfigs = pricingRangeRepository.getDynpConfigForDemandSurgeV2(isHoliday);
    if (CollectionUtils.isEmpty(pricingRangeConfigs)) {
      log.warn("No pricing range configurations found for demand surge calculation!");
      return;
    }
    log.info("Found {} pricing range configurations", pricingRangeConfigs.size());

    // Step 10: Join demand-supply and pricing range configurations into demand configurations
    var demandConfigs =
        joinSupplyDemandAndPricingRangeConfig(pricingRangeConfigs, demandSupplyInfos, batchCounter);

    // Step 11: Update dynamic surge entities based on the calculated demand configuration
    var dynpSurgeEntities =
        updateDynpSurgeEntitiesV2(dynpSurges, demandConfigs, zoneIdToNewPricingModelConfigMap);
    if (dynpSurgeEntities == null || dynpSurgeEntities.isEmpty()) {
      log.warn("No dynamic surge entities were generated or all entities are null");
      return;
    }
    log.info("Processing {} dynamic surge entities", dynpSurgeEntities.size());
    dynpSurgeEntities.forEach(
        entity -> {
          if (entity == null) {
            log.error("Null entity found in dynpSurgeEntities list{}", entity.toString());
          } else {
            log.debug(
                "Entity for zoneId: {}, surge: {}, lastUpdDt: {}, zonePriceModel: {}",
                entity.getZoneId(),
                entity.getSurge(),
                entity.getLastUpdDt(),
                entity.getZonePriceModel());
          }
        });

    // Step 12: Update the dynamic surge repository with the new entities
    dynamicSurgeRepository.updateDynpSurgesV2(dynpSurgeEntities);

    // Step 13: Update the cache with the latest dynamic surge entities
    log.info(
        "Caching dynamic surge entities - Size: {}",
        dynpSurgeEntities != null ? dynpSurgeEntities.size() : 0);
    log.info("Dynamic surge entities content: {}", dynpSurgeEntities);
    cacheService.deleteByKey(CommonConstant.DYNP_SURGES_NGP_KEY_CACHE);
    cacheService.setListValue(CommonConstant.DYNP_SURGES_NGP_KEY_CACHE, dynpSurgeEntities);

    // Step 14: Insert logs for the updated dynamic surge entities
    log.info("Start insert log for dynamic surge v2");
    dynpSurgeLogsRepository.insertDynpSurgesLogV2();
    log.info("Completed insert log for dynamic surge v2");

    // Sep 15: Log the completion of the demand-supply surge calculation process
    log.info(
        "Dynamic surge config has been updated and stored in the log. End of calculation of demand supply surge!");
  }

  /**
   * Determines if the current day is a company holiday.
   *
   * <p>This method primarily checks the cache for pre-loaded holiday data. If the cache is empty,
   * it falls back to the company holiday repository to determine if today is a holiday.
   *
   * @return {@code true} if the current day is a holiday based on configuration, {@code false}
   *     otherwise.
   * @throws InternalServerException If an error occurs while parsing holiday dates.
   */
  protected boolean getIsHolidayValueConfig() {
    // Step 1: Retrieve the list of company holidays from the cache
    final List<String> companyHolidays = cacheService.getCompanyHoliday();

    // Step 2: Check if the list of company holidays is not empty
    if (CollectionUtils.isNotEmpty(companyHolidays)) {
      // Step 3: Convert the current date to the required format (DD-MM-YYYY)
      final String currentDate = DateUtils.convertToSingDateInDDMMYYYFormat(new Date());

      // Step 4: Check if the current date matches any of the company holiday dates
      return companyHolidays.stream()
          .anyMatch(
              holiday -> {
                try {
                  // Convert holiday to the required format and compare with the current date
                  return currentDate.equals(
                      DateUtils.convertToSingDateInDDMMYYYFormat(
                          holiday, CommonConstant.YYYY_MM_DD_HH_MM_SS));
                } catch (ParseException e) {
                  // Step 5: Log and throw an exception if there is an error during date parsing
                  log.error("Parse error {}", holiday, e);
                  throw new InternalServerException(
                      PARSING_HOLIDAY_ERROR.getMessage(), PARSING_HOLIDAY_ERROR.getErrorCode());
                }
              });
    }

    // Step 6: If the cache is empty, check the repository for the current holiday
    return Objects.nonNull(companyHolidayRepository.getCurrentHolidayWithSingTimezone());
  }

  /**
   * Updates the list of dynamic surge entities based on the provided demand-supply configurations
   * and pricing models.
   *
   * @param dynpSurges Existing surge entities grouped by zone ID.
   * @param demandConfigs Demand-supply configurations calculated for dynamic pricing.
   * @param zoneIdToNewPricingModelConfigMap Mapping of zone IDs to pricing configurations.
   * @return A list of updated {@link DynamicSurgesEntity} objects.
   *     <p>This method uses the configured {@link SurgeCalculationStrategy} for each zone ID to
   *     calculate and apply new surge values. If an entity for a zone ID does not exist, a new
   *     entity is created and initialized.
   */
  private List<DynamicSurgesEntity> updateDynpSurgeEntitiesV2(
      List<DynamicSurgesEntity> dynpSurges,
      List<DemandSupplyConfigV2> demandConfigs,
      Map<String, NewPricingModelConfigEntity> zoneIdToNewPricingModelConfigMap) {
    if (dynpSurges == null || demandConfigs == null || zoneIdToNewPricingModelConfigMap == null) {
      log.warn("Received null input parameters in updateDynpSurgeEntitiesV2");
      return new ArrayList<>();
    }
    Map<String, DynamicSurgesEntity> zoneIdToDynamicSurgesMap =
        dynpSurges.stream()
            .collect(
                Collectors.toMap(
                    DynamicSurgesEntity::getZoneId, Function.identity(), (x1, x2) -> x2));

    return demandConfigs.stream()
        .filter(Objects::nonNull)
        .map(
            demandSupply -> {
              String zoneId = demandSupply.getZoneId();
              if (zoneId == null) {
                log.warn("Encountered null zoneId in demandSupply");
                return null;
              }
              DynamicSurgesEntity dynamicSurgesEntity = zoneIdToDynamicSurgesMap.get(zoneId);
              if (Objects.nonNull(dynamicSurgesEntity)) {
                var newPricingModelConfigEntity = zoneIdToNewPricingModelConfigMap.get(zoneId);
                var surgeCalcStrategy = getSurgeCalculationStrategy(newPricingModelConfigEntity);
                var oldSurge = dynamicSurgesEntity.getSurge();
                SurgeCalculationDto surgeCalculationDto =
                    SurgeCalculationDto.builder()
                        .demandConfig(demandSupply)
                        .newPricingModelConfigEntity(newPricingModelConfigEntity)
                        .currentSurge(oldSurge)
                        .build();

                var newSurge = surgeCalcStrategy.calculate(surgeCalculationDto);
                dynamicSurgesEntity.setSurge(newSurge);
                dynamicSurgesEntity.setZonePriceModel(surgeCalcStrategy.getVersion());
                dynamicSurgesEntity.setSurgeLow(demandSupply.getSurgeLow());
                dynamicSurgesEntity.setSurgeHigh(demandSupply.getSurgeHigh());
                dynamicSurgesEntity.setDemandRecent(demandSupply.getDemand30());
                dynamicSurgesEntity.setDemandPrevious(demandSupply.getDemand60());
                dynamicSurgesEntity.setDemandPredicted(demandSupply.getPredictedDemand30());
                dynamicSurgesEntity.setSupply(demandSupply.getSupply());
                dynamicSurgesEntity.setExcessDemand15(demandSupply.getExcessDemand15());
                dynamicSurgesEntity.setExcessDemand(demandSupply.getExcessDemand30());
                dynamicSurgesEntity.setLastUpdDt(new Timestamp(System.currentTimeMillis()));
                dynamicSurgesEntity.setPrevSurge(oldSurge);
                dynamicSurgesEntity.setPredictedDemand15(demandSupply.getPredictedDemand15());
                dynamicSurgesEntity.setUnmet15(demandSupply.getUnmet15());
                dynamicSurgesEntity.setPreviousUnmet15(demandSupply.getPreviousUnmet15());
                dynamicSurgesEntity.setDemand15(demandSupply.getDemand15());
                dynamicSurgesEntity.setBatchKey(demandSupply.getBatchCounter());
              } else {
                var newPricingModelConfigEntity = zoneIdToNewPricingModelConfigMap.get(zoneId);
                var surgeCalcStrategy = getSurgeCalculationStrategy(newPricingModelConfigEntity);
                var initialSurge = 0;
                SurgeCalculationDto surgeCalculationDto =
                    SurgeCalculationDto.builder()
                        .demandConfig(demandSupply)
                        .newPricingModelConfigEntity(newPricingModelConfigEntity)
                        .currentSurge(initialSurge)
                        .build();

                var newSurge = surgeCalcStrategy.calculate(surgeCalculationDto);
                dynamicSurgesEntity =
                    DynamicSurgesEntity.builder()
                        .zoneId(demandSupply.getZoneId())
                        .surge(newSurge)
                        .surgeLow(demandSupply.getSurgeLow())
                        .surgeHigh(demandSupply.getSurgeHigh())
                        .demandRecent(demandSupply.getDemand30())
                        .demandPrevious(demandSupply.getDemand60())
                        .demandPredicted(demandSupply.getPredictedDemand30())
                        .supply(demandSupply.getSupply())
                        .predictedDemand15(demandSupply.getPredictedDemand15())
                        .lastUpdDt(new Timestamp(System.currentTimeMillis()))
                        .zonePriceModel(surgeCalcStrategy.getVersion())
                        .unmet15(demandSupply.getUnmet15())
                        .previousUnmet15(demandSupply.getPreviousUnmet15())
                        .demand15(demandSupply.getDemand15())
                        .batchKey(demandSupply.getBatchCounter())
                        .excessDemand(demandSupply.getExcessDemand30())
                        .excessDemand15(demandSupply.getExcessDemand15())
                        .prevSurge(initialSurge)
                        .build();
              }
              return dynamicSurgesEntity;
            })
        .filter(Objects::nonNull)
        .toList();
  }

  /**
   * Determines the {@link SurgeCalculationStrategy} to be applied based on a specific pricing model
   * configuration.
   *
   * @param newPricingModelConfigEntity The pricing configuration entity for a zone.
   * @return The appropriate {@link SurgeCalculationStrategy}, defaulting to {@link
   *     SurgeCalculationV1Strategy} if no configuration is provided.
   *     <p>This method is flexible to support multiple versions of surge calculation, making it
   *     adaptable to updated pricing strategies.
   */
  private SurgeCalculationStrategy getSurgeCalculationStrategy(
      NewPricingModelConfigEntity newPricingModelConfigEntity) {
    if (newPricingModelConfigEntity == null) {
      return surgeCalculationV1Strategy;
    }

    if (newPricingModelConfigEntity.getZonePriceVersion() == null
        || V2.equalsIgnoreCase(newPricingModelConfigEntity.getZonePriceVersion())) {
      return surgeCalculationV2Strategy;
    }

    if (V2POINT5.equals(newPricingModelConfigEntity.getZonePriceVersion())) {
      return surgeCalculationV2Point5Strategy;
    }

    if (V3.equalsIgnoreCase(newPricingModelConfigEntity.getZonePriceVersion())) {
      return surgeCalculationV3Strategy;
    }

    return surgeCalculationV1Strategy;
  }

  /**
   * Retrieves and filters valid new pricing configurations grouped by zone ID.
   *
   * <p>Configurations are validated based on the current date and are grouped using each zone's ID.
   * If multiple configurations exist for a zone, the most relevant values (e.g., maximum rates) are
   * retained.
   *
   * @return A Map where keys are zone IDs, and values are {@link NewPricingModelConfigEntity}.
   */
  private Map<String, NewPricingModelConfigEntity> getValidNewPricingConfigurationsGroupByZoneId() {
    var now = OffsetDateTime.now();
    return configurationServiceOutboundPort.getNewPricingModelConfigEntities(true).stream()
        .filter(
            c ->
                (now.isEqual(c.getStartDt()) || now.isAfter(c.getStartDt()))
                    && now.isBefore(c.getEndDt()))
        .collect(
            Collectors.toMap(
                NewPricingModelConfigEntity::getZoneId,
                Function.identity(),
                (existing, current) -> {
                  existing.setAdditionalSurgeHigh(
                      ObjectUtils.max(
                          existing.getAdditionalSurgeHigh(), current.getAdditionalSurgeHigh()));
                  existing.setSurgeHighTierRate(
                      ObjectUtils.max(
                          existing.getSurgeHighTierRate(), current.getSurgeHighTierRate()));
                  existing.setUnmetRate1(
                      ObjectUtils.max(existing.getUnmetRate1(), current.getUnmetRate1()));
                  existing.setUnmetRate2(
                      ObjectUtils.max(existing.getUnmetRate2(), current.getUnmetRate2()));
                  existing.setNegativeDemandSupplyDownRate(
                      ObjectUtils.max(
                          existing.getNegativeDemandSupplyDownRate(),
                          current.getNegativeDemandSupplyDownRate()));
                  existing.setK1(ObjectUtils.max(existing.getK1(), current.getK1()));
                  existing.setK2(ObjectUtils.max(existing.getK2(), current.getK2()));
                  existing.setK3(ObjectUtils.max(existing.getK3(), current.getK3()));
                  existing.setK4(ObjectUtils.max(existing.getK4(), current.getK4()));
                  existing.setK5(ObjectUtils.max(existing.getK5(), current.getK5()));
                  existing.setK6(ObjectUtils.max(existing.getK6(), current.getK6()));
                  existing.setK7(ObjectUtils.max(existing.getK7(), current.getK7()));
                  existing.setK8(ObjectUtils.max(existing.getK8(), current.getK8()));
                  existing.setZonePriceVersion(
                      ObjectUtils.max(
                          existing.getZonePriceVersion(), current.getZonePriceVersion()));
                  return existing;
                }));
  }

  /**
   * Joins pricing range configurations and demand-supply statistics to create demand-supply
   * configuration entities ready for surge calculation.
   *
   * @param pricingRangeConfigs Pricing configurations related to demand-supply surge.
   * @param demandSupplyInfos Statistics on demand and supply across various zones.
   * @param batchCounter The batch identifier for the current calculation run.
   * @return A list of demand-supply configuration entities.
   *     <p>This method ensures that both pricing range and demand-supply data are seamlessly merged
   *     based on zone IDs. Default values are applied for any missing zone-related data.
   */
  private List<DemandSupplyConfigV2> joinSupplyDemandAndPricingRangeConfig(
      List<PricingRangeCalDemandSurgeQueryResponse> pricingRangeConfigs,
      List<DemandSupplyStatisticsResponseV2> demandSupplyInfos,
      int batchCounter) {

    if (CollectionUtils.isEmpty(pricingRangeConfigs)) {
      return Collections.emptyList();
    }

    // Convert the Dynp Configs list to Map with zoneId as the key
    Map<String, PricingRangeCalDemandSurgeQueryResponse> dynpConfigsByZoneId =
        pricingRangeConfigs.stream()
            .collect(
                Collectors.toMap(
                    PricingRangeCalDemandSurgeQueryResponse::getZoneId, Function.identity()));

    // Convert the Demand Supply Infos list to Map with zoneId as the key
    Map<String, DemandSupplyStatisticsResponseV2> demandSupplyByZoneId =
        demandSupplyInfos.stream()
            .collect(
                Collectors.toMap(DemandSupplyStatisticsResponseV2::getZoneId, Function.identity()));

    // Merge 2 maps dynp configs and demand supply infos, set default value for fields if zoneId
    // doesn't match
    return Stream.concat(
            dynpConfigsByZoneId.keySet().stream(), demandSupplyByZoneId.keySet().stream())
        .distinct()
        .map(
            zoneId -> {
              final DemandSupplyStatisticsResponseV2 demandSupply =
                  demandSupplyByZoneId.get(zoneId);
              final PricingRangeCalDemandSurgeQueryResponse pricingRangeConfig =
                  dynpConfigsByZoneId.get(zoneId);
              var demandSupplyConfigV2 = this.buildDefaultDemandSupplyConfigV2();
              demandSupplyConfigV2.setZoneId(zoneId);
              demandSupplyConfigV2.setBatchCounter(batchCounter);

              if (Objects.nonNull(demandSupply)) {
                mapDemandSupplyStatisticToDemandSupply(demandSupplyConfigV2, demandSupply);
              }

              if (Objects.nonNull(pricingRangeConfig)) {
                mapPricingRangeConfigToDemandSupplyConfig(demandSupplyConfigV2, pricingRangeConfig);
              }
              return demandSupplyConfigV2;
            })
        .toList();
  }

  /**
   * Maps a single pricing range configuration to a demand-supply configuration object.
   *
   * @param demandSupplyConfigV2 The demand-supply configuration to be updated.
   * @param pricingRangeConfig The pricing range configuration to map.
   */
  private static void mapPricingRangeConfigToDemandSupplyConfig(
      DemandSupplyConfigV2 demandSupplyConfigV2,
      PricingRangeCalDemandSurgeQueryResponse pricingRangeConfig) {
    demandSupplyConfigV2.setSurgeLow(pricingRangeConfig.getSurgeLow());
    demandSupplyConfigV2.setSurgeHigh(pricingRangeConfig.getSurgeHigh());
    demandSupplyConfigV2.setStepNegative(pricingRangeConfig.getStepNegative());
    demandSupplyConfigV2.setStepPositive(pricingRangeConfig.getStepPositive());
  }

  /**
   * Maps demand-supply statistics to a demand-supply configuration entity.
   *
   * @param demandSupplyConfigV2 The demand-supply configuration entity.
   * @param demandSupply The demand-supply statistics.
   */
  private static void mapDemandSupplyStatisticToDemandSupply(
      DemandSupplyConfigV2 demandSupplyConfigV2, DemandSupplyStatisticsResponseV2 demandSupply) {
    demandSupplyConfigV2.setSupply(demandSupply.getSupply());
    demandSupplyConfigV2.setDemand15(demandSupply.getDemand15());
    demandSupplyConfigV2.setDemand30(demandSupply.getDemand30());
    demandSupplyConfigV2.setDemand60(demandSupply.getDemand60());
    demandSupplyConfigV2.setPredictedDemand30(demandSupply.getPredictedDemand30());
    demandSupplyConfigV2.setPredictedDemand15(demandSupply.getPredictedDemand15());
    demandSupplyConfigV2.setPreviousDemand15(demandSupply.getPreviousDemand15());
    demandSupplyConfigV2.setExcessDemand15(demandSupply.getExcessDemand15());
    demandSupplyConfigV2.setExcessDemand30(demandSupply.getExcessDemand30());
    demandSupplyConfigV2.setUnmet15(demandSupply.getUnmet15());
    demandSupplyConfigV2.setPreviousUnmet15(demandSupply.getPreviousUnmet15());
  }

  /**
   * Creates and initializes a default {@link DemandSupplyConfigV2} entity.
   *
   * <p>The entity is pre-filled with zero-values to ensure a consistent baseline, which can be
   * updated based on incoming configurations and statistics.
   *
   * @return An instance of {@link DemandSupplyConfigV2} with default values.
   */
  protected DemandSupplyConfigV2 buildDefaultDemandSupplyConfigV2() {
    return DemandSupplyConfigV2.builder()
        .surgeLow(0)
        .surgeHigh(0)
        .stepNegative(0)
        .stepPositive(0)
        .demand30(0)
        .demand60(0)
        .predictedDemand30(0)
        .predictedDemand15(0)
        .unmet15(0)
        .previousUnmet15(0)
        .demand15(0)
        .supply(0)
        .build();
  }

  /**
   * Retrieves the current list of dynamic surge entities, either from the cache or the repository
   * if the cache is empty.
   *
   * @return A list of {@link DynamicSurgesEntity} objects representing existing surges.
   */
  public List<DynamicSurgesEntity> getDynpSurges() {
    List<DynamicSurgesEntity> dynpSurges =
        cacheService.getListValue(
            CommonConstant.DYNP_SURGES_NGP_KEY_CACHE, DynamicSurgesEntity.class);
    if (CollectionUtils.isEmpty(dynpSurges)) {
      log.info("Dynp surge from cache is empty!");
      return dynamicSurgeRepository.getNGPDynpSurges();
    }
    return dynpSurges;
  }
}
