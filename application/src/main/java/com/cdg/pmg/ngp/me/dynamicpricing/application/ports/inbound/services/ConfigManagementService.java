package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.cbdcharge.LocReloadCache;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import java.util.List;

/** The interface Config management service. */
public interface ConfigManagementService {

  /** Reload all configuration Cache */
  String reloadAllConfig();

  /** Reload flat fare config Cache */
  String reloadFlatFareConfig();

  /** Reload fare type config Cache */
  String reloadFareTypeConfig();

  /** Reload company holiday config Cache */
  String reloadCompanyHolidayConfig();

  /** Reload dynamic surge config Cache */
  String reloadLocationSurchargeConfig();

  /** Reload dynamic surge config Cache */
  String reloadDynpSurgeConfig();

  /** Setup flat fare config set for limo, standard, estStandard */
  void setupFlatFareConfigSet();

  /** Setup Fare Type Config Set */
  void setupCommonConfigSet();

  /** Reload configurations if empty. */
  void checkEmptyAndReloadConfigs();

  /**
   * Get all Location Surcharge Config by day in week
   *
   * @return location surcharge config
   */
  List<LocationSurchargeConfig> getAllLocationSurchargeConfig(String dayInWeek);

  /**
   * Get all dos surge config on Cache
   *
   * @return dos surge list
   */
  List<DynamicSurgesEntity> getAllDOSSurgeCache();

  /**
   * Get all r1 dos surge config on Cache
   *
   * @return dos surge list
   */
  List<DynamicSurgesEntity> getAllR1DOSSurgeCache();

  /**
   * Get all r2 dos surge config on Cache
   *
   * @return dos surge list
   */
  List<DynamicSurgesEntity> getAllR2DOSSurgeCache();

  /**
   * Get all fare type config on Cache
   *
   * @return DynamicPricingConfigSet
   */
  DynamicPricingConfigSet getAllFareTypeConfigCache();

  /**
   * Reload CBD Address config Cache
   *
   * @param locReloadCache cbdReloadCache
   */
  void reloadCBDAddressConfig(LocReloadCache locReloadCache);
}
