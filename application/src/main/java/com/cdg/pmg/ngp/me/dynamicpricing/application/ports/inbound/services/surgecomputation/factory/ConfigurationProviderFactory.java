package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.LiveStandardInputProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.StaticRegionBasedConfigurationProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.StaticTimeBasedConfigurationProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.MappingTypeEnum;
import java.math.BigDecimal;
import java.util.EnumMap;
import java.util.Map;

/**
 * Factory class for creating configuration data providers.
 *
 * @see ConfigurationDataProvider
 * @see MappingTypeEnum
 */
public class ConfigurationProviderFactory {
  private final Map<MappingTypeEnum, ConfigurationDataProvider> providers =
      new EnumMap<>(MappingTypeEnum.class);

  public ConfigurationProviderFactory(
      Map<String, Map<DayOfWeekEnum, Map<Integer, String>>> staticTimeConfig,
      Map<String, Map<Long, String>> staticRegionConfig,
      Map<Long /* regionId */, Map<String /* name */, BigDecimal>> standardInputValueMap,
      boolean isHoliday) {

    providers.put(
        MappingTypeEnum.STATIC_TIME_BASED_CONFIGURATION,
        new StaticTimeBasedConfigurationProvider(staticTimeConfig, isHoliday));

    providers.put(
        MappingTypeEnum.STATIC_REGION_BASED_CONFIGURATION,
        new StaticRegionBasedConfigurationProvider(staticRegionConfig));

    providers.put(
        MappingTypeEnum.LIVE_STANDARD_INPUT, new LiveStandardInputProvider(standardInputValueMap));
  }

  public ConfigurationDataProvider getProvider(MappingTypeEnum type) {
    return providers.get(type);
  }
}
