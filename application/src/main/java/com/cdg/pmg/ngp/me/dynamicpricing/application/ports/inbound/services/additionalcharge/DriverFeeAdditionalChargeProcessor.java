package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.AdditionalChargeFeeConfigResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
@ServiceComponent
@Slf4j
public class DriverFeeAdditionalChargeProcessor
    implements AdditionalChargeProcessor<FlatFareVOPart, AdditionalChargeDriverFeeData> {

  public static final String CHARGE_THRESHOLD = "charge_threshold";
  public static final String CHARGE_LOWER_VALUE = "charge_lower_value";
  public static final String CHARGE_UPPER_VALUE = "charge_upper_value";
  public static final String IS_COUNT_IN_TOTALFARE = "is_count_in_totalfare";
  public static final String IS_OPTIONAL = "is_optional";
  public static final String VEHICLE_ATTRIBUTE_ID = "vehicle_attribute_id";
  public static final String DRIVER_FEE = "DRIVER_FEE";

  @Override
  public List<AdditionalChargeDriverFeeData> calculateAdditionalCharge(
      FlatFareVOPart calculateParam,
      Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap,
      boolean isNeedCalculateDriverFee,
      String bookingChannel) {
    log.info(
        "process calculateAdditionalCharge, Start calculating with calculateParam: {}, additionalChargeFeeConfigMap: {}, isNeedCalculateDriverFee: {}, bookingChannel: {}",
        calculateParam,
        additionalChargeFeeConfigMap,
        isNeedCalculateDriverFee,
        bookingChannel);
    if (calculateParam == null) {
      log.warn("process calculateAdditionalCharge, calculateParam is null, now end.");
      return List.of();
    }

    if (null == additionalChargeFeeConfigMap || additionalChargeFeeConfigMap.isEmpty()) {
      log.warn(
          "process calculateAdditionalCharge, additionalChargeFeeConfigMap is null or empty, now end.");
      return List.of();
    }

    List<AdditionalChargeDriverFeeData> additionalCharges = new ArrayList<>();
    for (var additionalChargeConfig : additionalChargeFeeConfigMap.entrySet()) {
      try {
        /*
        1.Transfer additional charge config list to map.
         */
        List<AdditionalChargeFeeConfigResponse> additionalChargeFeeConfigList =
            additionalChargeConfig.getValue();
        /*
        Below map, key is chargeKey, value is AdditionalChargeFeeConfigResponse.
         */
        Map<String, AdditionalChargeFeeConfigResponse> additionalChargeFeeKeyConfigMap =
            new HashMap<>();

        Optional.ofNullable(additionalChargeFeeConfigList)
            .ifPresentOrElse(
                additionalChargeFeeConfig ->
                    additionalChargeFeeKeyConfigMap.putAll(
                        additionalChargeFeeConfig.stream()
                            .collect(
                                Collectors.toMap(
                                    AdditionalChargeFeeConfigResponse::getChargeKey,
                                    Function.identity(),
                                    (oldData, newData) -> newData))),
                () ->
                    log.warn(
                        "There is no additional charge fee config from FareService for chargeType={}.",
                        additionalChargeConfig.getKey()));
        /*
        2.If there is no additional charge config get from fare-svc, will not do calculate, continue with the next config
         */
        if (additionalChargeFeeKeyConfigMap.isEmpty()) {
          log.warn(
              "process calculateAdditionalCharge, additionalChargeFeeKeyConfigMap is empty, now end.");
          continue;
        }

        if (isSkipCalculate(
            additionalChargeFeeKeyConfigMap, isNeedCalculateDriverFee, bookingChannel)) {
          /*
          3.If additional charge config without "is_count_in_totalfare",or "is_count_in_totalfare"charge_formual is "false",
          will skip calculate and return null.
           */
          log.warn(
              "process calculateAdditionalCharge, is_count_in_totalfare is null or false, now skip and end.");
          continue;
        }
        /*
        4.Calculate additional charge for totalFare、estimatedFareLF、estimatedFareRT,return the calculate result.
         */
        final Double vehicleAttributeId =
            additionalChargeFeeKeyConfigMap.get(VEHICLE_ATTRIBUTE_ID) == null
                ? null
                : additionalChargeFeeKeyConfigMap.get(VEHICLE_ATTRIBUTE_ID).getChargeValue();
        final String isOption =
            additionalChargeFeeKeyConfigMap.get(IS_OPTIONAL) == null
                ? null
                : additionalChargeFeeKeyConfigMap.get(IS_OPTIONAL).getChargeFormula();
        AdditionalChargeDriverFeeData additionalChargeDriverFeeData =
            AdditionalChargeDriverFeeData.builder()
                .chargeId(additionalChargeFeeConfigList.get(0).getChargeId())
                .chargeType(additionalChargeFeeConfigList.get(0).getChargeType())
                .totalFareDriverFee(
                    calculateAdditionalChargeFee(
                        calculateParam.getTotalFare(), additionalChargeFeeKeyConfigMap))
                .estimatedFareLFDriverFee(
                    calculateAdditionalChargeFee(
                        calculateParam.getEstimatedFareLF(), additionalChargeFeeKeyConfigMap))
                .estimatedFareRTDriverFee(
                    calculateAdditionalChargeFee(
                        calculateParam.getEstimatedFareRT(), additionalChargeFeeKeyConfigMap))
                .isOptional(BooleanUtils.toBoolean(isOption))
                .vehicleAttributeId(
                    vehicleAttributeId == null ? null : vehicleAttributeId.intValue())
                .build();

        /*
        5.Set additional charge config data(charge_threshold,charge_lower_value,charge_upper_value)
        to the return AdditionalChargeDriverFeeData.
         */
        Optional.ofNullable(additionalChargeFeeKeyConfigMap.get(CHARGE_THRESHOLD))
            .ifPresentOrElse(
                chargeThresholdConfig ->
                    additionalChargeDriverFeeData.setChargeThreshold(
                        chargeThresholdConfig.getChargeValue()),
                () ->
                    log.warn(
                        "There is no additional charge charge_threshold config from FareService."));

        Optional.ofNullable(additionalChargeFeeKeyConfigMap.get(CHARGE_LOWER_VALUE))
            .ifPresentOrElse(
                chargeLowerValue ->
                    additionalChargeDriverFeeData.setChargeLowerLimit(
                        chargeLowerValue.getChargeValue()),
                () ->
                    log.warn(
                        "There is no additional charge charge_lower_value config from FareService."));

        Optional.ofNullable(additionalChargeFeeKeyConfigMap.get(CHARGE_UPPER_VALUE))
            .ifPresentOrElse(
                chargeUpperValue ->
                    additionalChargeDriverFeeData.setChargeUpperLimit(
                        chargeUpperValue.getChargeValue()),
                () ->
                    log.warn(
                        "There is no additional charge charge_upper_value config from FareService."));

        additionalCharges.add(additionalChargeDriverFeeData);
      } catch (InternalServerException e) {
        log.error("Failed to get additional charge fee config and process failed.", e);
      }
    }

    return additionalCharges;
  }

  /**
   * Calculate additional charge fee. If originFare > charge_threshold, then return
   * charge_upper_value, If originFare <= charge_threshold, then return charge_lower_value.
   *
   * @param originFare origin fare
   * @param additionalChargeFeeKeyConfigMap additional charge fee config use for calculate
   * @return fare added additional charge fee
   */
  private BigDecimal calculateAdditionalChargeFee(
      BigDecimal originFare,
      Map<String, AdditionalChargeFeeConfigResponse> additionalChargeFeeKeyConfigMap) {

    log.info(
        "Start to calculate additional charge fee originFare={} additionalChargeFeeKeyConfigMap={}.",
        originFare,
        additionalChargeFeeKeyConfigMap);
    if (ObjectUtils.isEmpty(originFare)) {
      return null;
    }
    // If any additional charge fee config is null , then will not calculate
    AdditionalChargeFeeConfigResponse chargeThresholdObject =
        additionalChargeFeeKeyConfigMap.get(CHARGE_THRESHOLD);
    AdditionalChargeFeeConfigResponse chargeLowerValueObject =
        additionalChargeFeeKeyConfigMap.get(CHARGE_LOWER_VALUE);
    AdditionalChargeFeeConfigResponse chargeUpperValueObject =
        additionalChargeFeeKeyConfigMap.get(CHARGE_UPPER_VALUE);
    if (ObjectUtils.isEmpty(chargeThresholdObject)
        || ObjectUtils.isEmpty(chargeLowerValueObject)
        || ObjectUtils.isEmpty(chargeUpperValueObject)) {
      return null;
    }
    // If any additional charge fee config value is null , then will not calculate
    Double chargeThreshold = chargeThresholdObject.getChargeValue();
    Double chargeLowerValue = chargeLowerValueObject.getChargeValue();
    Double chargeUpperValue = chargeUpperValueObject.getChargeValue();
    if (ObjectUtils.isEmpty(chargeThreshold)
        || ObjectUtils.isEmpty(chargeLowerValue)
        || ObjectUtils.isEmpty(chargeUpperValue)) {
      return null;
    }
    // value > chargeThreshold ,additional charge fee = chargeUpperValue
    // value <= chargeThreshold ,additional charge fee = chargeLowerValue
    // when exception ,return null
    try {
      if (originFare.doubleValue() > chargeThreshold) {
        return BigDecimal.valueOf(chargeUpperValue);
      } else {
        return BigDecimal.valueOf(chargeLowerValue);
      }
    } catch (Exception e) {
      log.error("Calculate additional charge fee error, will return null.", e);
      return null;
    }
  }

  /**
   * Judge if is_count_in_totalfare equal "true", or this method will return true to skip additional
   * charge calculate.
   *
   * @param additionalChargeFeeKeyConfigMap additional charge fee config map
   * @return if isCountInTotalFare
   */
  private boolean isSkipCalculate(
      Map<String, AdditionalChargeFeeConfigResponse> additionalChargeFeeKeyConfigMap,
      boolean isNeedCalculateDriverFee,
      String bookingChannel) {
    AdditionalChargeFeeConfigResponse isCountInTotalFare =
        additionalChargeFeeKeyConfigMap.get(IS_COUNT_IN_TOTALFARE);

    if (ObjectUtils.isEmpty(isCountInTotalFare)) {
      return true;
    }

    // get charge type
    final String chargeType = isCountInTotalFare.getChargeType();

    // if chargeType is DRIVER_FEE and isNeedCalculateDriverFee is false, then skip calculate
    if (DRIVER_FEE.equals(chargeType) && !isNeedCalculateDriverFee) {
      return true;
    }

    // for other charge types, the bookingChannel (from the request) must be in bookingChannels in
    // the additional charge config
    final String additionalChargeConfigBookingChannels = isCountInTotalFare.getBookingChannel();
    final boolean invalid =
        !DRIVER_FEE.equals(chargeType)
            && (StringUtils.isBlank(bookingChannel)
                || StringUtils.isNotEmpty(bookingChannel)
                    && !additionalChargeConfigBookingChannels.contains(bookingChannel));
    if (invalid) {
      return true;
    }

    return !Boolean.TRUE.toString().equalsIgnoreCase(isCountInTotalFare.getChargeFormula());
  }
}
