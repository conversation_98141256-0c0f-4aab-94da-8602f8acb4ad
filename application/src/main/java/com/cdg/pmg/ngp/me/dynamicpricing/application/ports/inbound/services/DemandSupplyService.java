package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.DynamicSurgesEntity;
import java.util.List;

public interface DemandSupplyService {

  /** Calculate demand supply surge. */
  void calculateDemandSupplySurge();

  /** Get dynp surge config from cache, if null, get from database */
  List<DynamicSurgesEntity> getDynpSurges();

  /** Get r1 dynp surge config from cache, if null, get from database */
  List<DynamicSurgesEntity> getR1DynpSurges();

  /** Get r2 dynp surge config from cache, if null, get from database */
  List<DynamicSurgesEntity> getR2DynpSurges();

  /** Load all dynamic surges config to cache. */
  void loadDynpSurgeConfigs();

  /** Load all dynamic surges NGP config to cache. */
  void loadDynpSurgeNgpConfigs();

  /** Calculate demand supply surge ngp. */
  void calculateDemandSupplySurgeNgp();
}
