package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.AdditionalChargeConfigItem;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface AdditionalChargeMapper {

  /**
   * Map list AdditionalChargeConfigData to list AdditionalChargeConfigItem
   *
   * @param additionalCharges additional charges
   * @return a list of AdditionalChargeConfigItem
   */
  List<AdditionalChargeConfigItem> mapToAdditionalChargeConfigItems(
      List<AdditionalChargeConfigData> additionalCharges);

  /**
   * Map AdditionalChargeConfigData to AdditionalChargeConfigItem
   *
   * @param additionalCharge an additional charge
   * @return an AdditionalChargeConfigItem
   */
  AdditionalChargeConfigItem mapToAdditionalChargeConfigItem(
      AdditionalChargeConfigData additionalCharge);
}
