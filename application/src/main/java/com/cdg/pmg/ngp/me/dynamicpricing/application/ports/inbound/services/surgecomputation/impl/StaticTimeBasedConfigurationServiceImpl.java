package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StaticTimeBasedConfigurationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.ConfigurationVersionUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.StaticTimeBasedConfigurationRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ServiceComponent
@RequiredArgsConstructor
public class StaticTimeBasedConfigurationServiceImpl
    implements StaticTimeBasedConfigurationService {

  private final StaticTimeBasedConfigurationRepository configurationRepository;

  @Override
  public List<StaticBasedConfigurationVersionEntity> getStaticTimeBasedConfigurationVersions() {
    List<StaticBasedConfigurationVersionEntity> versions =
        configurationRepository.findAllVersions();

    ConfigurationVersionUtils.updateIsInUse(versions);
    return versions;
  }

  @Override
  public List<StaticTimeBasedConfigurationEntity> batchCreateStaticTimeBasedConfiguration(
      List<StaticTimeBasedConfigurationEntity> configurations) {
    checkVersionAndEffectivePeriod(configurations);

    try {
      return configurationRepository.batchCreateTimeRegionBasedConfigurations(configurations);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("Overlapping effective date ranges")) {
        log.error(
            "[batchCreateStaticTimeBasedConfiguration] Error create time based configuration with overlapping effective date ranges: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR
                .getErrorCode());
      }

      log.error(
          "[batchCreateStaticTimeBasedConfiguration] Error create time based configuration: ", e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_CREATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_CREATE_ERROR.getErrorCode());
    }
  }

  @Override
  public List<StaticTimeBasedConfigurationEntity> getStaticTimeBasedConfigurations(String version) {
    return configurationRepository.findAllByVersion(version);
  }

  @Override
  public StaticTimeBasedConfigurationEntity getSurgeComputationTimeBasedStaticConfigurationById(
      Long id) {
    return configurationRepository.findById(id).orElse(null);
  }

  @Override
  public StaticTimeBasedConfigurationEntity updateSurgeComputationTimeBasedStaticConfiguration(
      Long id, StaticTimeBasedConfigurationEntity configuration, String userId) {
    configuration.setId(id);
    configuration.setUpdatedBy(userId);
    configuration.setUpdatedDate(Instant.now());

    try {
      return updateSurgeComputationTimeBasedStaticConfiguration(configuration);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("Overlapping effective date ranges")) {
        log.error(
            "[updateSurgeComputationTimeBasedStaticConfiguration] Error update time based configuration with overlapping effective date ranges: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_OVERLAPPING_DATES_ERROR
                .getErrorCode());
      }

      log.error(
          "[updateSurgeComputationTimeBasedStaticConfiguration] Error update time based configuration: ",
          e);
      throw new InternalServerException(
          SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_UPDATE_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_TIME_BASED_CONFIGURATION_UPDATE_ERROR.getErrorCode());
    }
  }

  @Override
  public StaticTimeBasedConfigurationEntity updateSurgeComputationTimeBasedStaticConfiguration(
      StaticTimeBasedConfigurationEntity request) {
    Optional<StaticTimeBasedConfigurationEntity> existingConfig =
        configurationRepository.findById(request.getId());

    if (existingConfig.isEmpty()) {
      return null;
    }

    return configurationRepository.save(request);
  }

  @Override
  public boolean deleteSurgeComputationTimeBasedStaticConfiguration(Long id, String userId) {
    Optional<StaticTimeBasedConfigurationEntity> existingConfig =
        configurationRepository.findById(id);

    if (existingConfig.isEmpty()) {
      return false;
    }

    StaticTimeBasedConfigurationEntity config = existingConfig.get();
    config.setUpdatedBy(userId);
    config.setUpdatedDate(Instant.now());

    configurationRepository.save(config);
    configurationRepository.deleteById(id);
    return true;
  }

  @Override
  public StaticBasedConfigurationEffectiveCheckEntity effectiveCheck() {
    return configurationRepository.effectiveCheck();
  }

  /**
   * Check the version and effectiveFrom and effectiveTo are unique in the configuration list.
   *
   * @param configurations the list of configuration neet to be checked
   * @throws BadRequestException if the version and effectiveFrom and effectiveTo are not unique
   */
  private static void checkVersionAndEffectivePeriod(
      final List<StaticTimeBasedConfigurationEntity> configurations) {
    Set<String> checkSet =
        configurations.stream()
            .map(v -> v.getVersion() + ":" + v.getEffectiveFrom() + ":" + v.getEffectiveTo())
            .collect(Collectors.toSet());
    if (checkSet.size() != 1) {
      throw new BadRequestException(
          SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR.getMessage(),
          SURGE_COMPUTATION_MODEL_STATIC_BASED_CONFIGURATION_VERSION_NOT_SAME_ERROR.getErrorCode());
    }
  }
}
