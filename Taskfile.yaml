version: 3

includes:
  common:
    taskfile: /taskfiles # This is common taskfile that is included in docker image
    optional: true

env:
  SHA: '{{ .SHA }}' # Commit hash that triggered pipeline
  WORKSPACE: '{{ .WORKSPACE }}' # It will be injected by CI
  REPO_REVISION: '{{ .REPO_REVISION }}' # Branch or tag that triggered pipeline
  APP_NAME: 'me-dynamicpricing-app'
  HELM_CHART: *****************:cdgtaxi/me-k8s.git
  VALUE_FILE: me-dynamicpricing-values.yaml
  AWS_DEFAULT_REGION: 'ap-southeast-1'
  IMAGE_REGISTRY: '************.dkr.ecr.{{ .AWS_DEFAULT_REGION }}.amazonaws.com'
  IMAGE_NAME: '{{ .IMAGE_REGISTRY }}/{{ .APP_NAME }}'
  IMAGE_NAME_FULL: '{{ .IMAGE_NAME }}:{{ substr 0 8 .SHA }}'
  DOCKERFILE: 'Dockerfile'
  SONAR_HOST: https://sonarcloud.io
  SONAR_PROJECT:  cdgtaxi_ngp-me-dynamicpricing-svc
  SONAR_ORGANIZATION: cdg-zig
  SONAR_BRANCH: '{{ .REPO_REVISION }}'
dotenv:
  - .env

vars:
  ENVIRONMENT:
    sh: echo $([[ {{ .REPO_REVISION }} == "sit" ]] && echo "sit" || ([[ {{ .REPO_REVISION }} == "uat" || {{ .REPO_REVISION }} == "uat1" ]] && echo "uat1") || ([[ {{ .REPO_REVISION }} == "main" ]] && echo "prod") || echo "sit")
  WORKLOAD_ACCOUNT_ID:
    sh: echo $([[ "{{ .REPO_REVISION }}" == "main" ]] && echo "************" || echo "************")
  IMAGE_REGISTRY:
    sh: echo '{{.WORKLOAD_ACCOUNT_ID }}.dkr.ecr.{{ .AWS_DEFAULT_REGION }}.amazonaws.com'
  IMAGE_NAME:
    sh: echo '{{ .IMAGE_REGISTRY }}/{{ .APP_NAME }}'
  IMAGE_NAME_FULL:
    sh: echo '{{ .IMAGE_NAME }}:{{ substr 0 8 .SHA }}'

  secrets-manager:
    map:
      {
        SONAR_TOKEN: 'arn:aws:secretsmanager:ap-southeast-1:************:secret:ngp-terraform/sonarcloud:SONAR_TOKEN',
        DB_PASSWORD: 'arn:aws:secretsmanager:ap-southeast-1:{{.WORKLOAD_ACCOUNT_ID}}:secret:/secrets/ngp/uat1/ngp-me-dynamicpricing-svc:liquibase.password',
        DB_USERNAME: 'arn:aws:secretsmanager:ap-southeast-1:{{.WORKLOAD_ACCOUNT_ID}}:secret:/secrets/ngp/uat1/ngp-me-dynamicpricing-svc:liquibase.username',
        JDBC_CONNECTION: 'arn:aws:secretsmanager:ap-southeast-1:{{.WORKLOAD_ACCOUNT_ID}}:secret:/secrets/ngp/uat1/ngp-me-dynamicpricing-svc:spring.datasource.url'
      }

tasks:
  liquibase:
    cmds:
      - echo "Running liquibase update"
      - ./gradlew :techframework:update -Purl=$JDBC_CONNECTION -Pusername=$DB_USERNAME -Ppassword=$DB_PASSWORD
  init:
    cmds:
      - touch .env
      - echo "Start integrating secret"
      - for: {var: secrets-manager}
        cmd: task common:aws:parse-secrets-manager SECRET_KEY={{.KEY}} SECRET_SPEC={{.ITEM}}
      - for: {var: parameter-store}
        cmd: task common:aws:parse-paramater-store PARAM_KEY={{.KEY}} PARAM_SPEC={{.ITEM}}
  install:
    cmds:
      - echo "Install and Download"
      - aws s3api get-object --bucket rediscloud-ca-cert --key redis_ca.pem redis_ca.pem
    internal: true
  pre_build:
    cmds:
      - task: install
    internal: true
  post_build:
    cmds:
      - echo -n {{ .IMAGE_NAME_FULL }} > {{ .IMAGE_RESULT }}
      - echo -n {{ .HELM_CHART }} > {{ .HELM_CHART_RESULT }}
      - echo -n {{ .VALUE_FILE }} > {{ .VALUE_FILE_RESULT }}
    internal: true
  build:
    cmds:
      - task: pre_build
      - defer:
          task: post_build
      # Build the microservice
      - echo "Building the project"
      - ./gradlew clean build -x test -x integrationTest
      - cmd: aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | buildah login --username AWS --password-stdin ${IMAGE_REGISTRY}
      # Dev to edit from here
      - task: 'common:buildah'
        vars:
          TAG: '{{ .IMAGE_NAME_FULL }}'
          DOCKERFILE: '{{ .DOCKERFILE }}'
          OTHER_OPTS: >-
            --build-arg profile=uat1-eks,json-logging
            --build-arg ECR_URI=$IMAGE_REGISTRY 
            --build-arg SONAR_TOKEN=$SONAR_TOKEN
            --build-arg SONAR_HOST=$SONAR_HOST
            --build-arg SONAR_PROJECT=$SONAR_PROJECT
            --build-arg SONAR_ORGANIZATION=$SONAR_ORGANIZATION
            --build-arg SONAR_BRANCH=$SONAR_BRANCH