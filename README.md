# NGP ME Dynamic Pricing Service
## Table of Content
1. [Introduction](#1-introduction)   
2. [Software Architecture](#2-software-architecture)    
3. [Use Case Implementation](#3-use-case-implementation)   
4. [Developer Guide](#4-developer-guide)   
   4.1. [Folder Structure](#41-folder-structure)      
   4.2. [Running Locally](#42-running-locally)   
5. [Scheduled Jobs](#5-scheduled-jobs)   
6. [Configuration Parameters](#6-configuration-parameters)   
   6.1. [AWS Secrets Manager](#61-aws-secrets-manager)   
   6.2. [AWS Parameter Store](#62-aws-parameter-store)   
   6.3. [NGP Configuration Management Service](#63-ngp-configuration-management-service)   
7. [CI/CD](#7-cicd)   
8. [Swagger Links](#8-swagger-links)   
9. [Glossary](#9-glossary)      
10. [Appendix](#10-appendix)   


## 1. Introduction
<a name="1-introduction"></a>
In Zig Application, the fare that the passenger pays is determined by a combination of factors:   
![](docs/fare_breakdown.drawio.svg)

The me-dynamicpricing-service is the service that provides calculation of surge pricing based on the demand and supply of 
drivers and passengers.   

To clarify any potential confusion, the me-dynamicpricing-service handles the dynamic pricing component of a fare.
In contrast, the ngp-me-fare-service manages the static aspects of a fare, which include peak-hour pricing, platform fees, 
commissions, and other related components.   

Key features provided by the service include:    
<ul>
  <li>Calculation of Multi-Fare</li>
  <li>Parameter Configuration Management for Dynamic Pricing</li>
  <li>Calculation of Demand Surges</li>
  <li>Load Configuration to Cache</li>
  <li>Get Generated Routed by TripId</li>
  <li>Validate Fare for Booking</li>
  <li>Store Fare Breakdown</li>
  <li>Fare Upload</li>
  <li>Get Multi-Fare Data in Cache by FareId</li>
  <li>Search Fare Breakdown</li>
  <li>Get Location Surcharge</li>
  <li>Get Book-A-Ride Configurations</li>
</ul>

The following diagram illustrates the role of the service within the Mobility Engine:   
![](docs/top_level_architecture.drawio.svg)

## 2. Software Architecture
<a name="2-software-architecture"></a>
<b>Note:</b>
The following diagram provides a general flow of logic in the dynamicpricing-svc.   
The actual implementation may vary slightly.


## 3. Use Case Implementation
<a name="3-use-case-implementation"></a>

<b>Note:</b><br/>
Some use-cases have been deprecated, so the reference numbers may have gaps between them.<br/>
Also note that confluence links contain the original designs, while git links reflect the current implementation.<br/> 
Both content shall be unified in the near future.

<ul>
  <li>Reference: ******** - Calculate MultiFare Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1138622515/********.1+Calculate+MultiFare+Use+Case+Design">Confluence</a></li>
      <li><a href="docs/sequence_diagrams/********.1_calculate_multi_fare.drawio.svg">Git</a></li>
    </ul>
  </li>
  <li>Reference: 4.2.12.2 - Parameter Configuration Management Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1138557005/********.2+Parameter+config+for+Dynamic+pricing">Confluence</a></li>
      <li><a href="docs/sequence_diagrams/********.2_parameter_config_management_get_fare_type_config.drawio.svg">Git - Get Fare Type Configurations</a></li>
      <li><a href="docs/sequence_diagrams/********.2_parameter_config_management_insert_or_update_fare_type_config.drawio.svg">Git - Insert/Update Fare Type Configurations</a></li>
      <li><a href="docs/sequence_diagrams/********.2_parameter_config_management_get_pricing_range_config.drawio.svg">Git - Get Pricing Range Configurations</a></li>
      <li><a href="docs/sequence_diagrams/********.2_parameter_config_management_insert_or_update_pricing_range_config.drawio.svg">Git - Insert/Update Fare Type Configurations</a></li>
    </ul>
  </li>
  <li>Reference: 4.2.12.4 - Calculation of Demand Surges Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1138622515/********.1+Calculate+MultiFare+Use+Case+Design">Confluence</a></li>
      <li><a href="docs/sequence_diagrams/********.4_update_dynamic_surge.drawio.svg">Git</a></li>
    </ul>
  </li>
  <li>Reference: ******** - Load Config to Cache Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1144816350/********.5+Load+configs+to+Cache">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ******** - Get Generated Route by TripId Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1176830013/********.6+Get+generated+route+by+tripId+Use+Case+Design">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ******** - Validate Fare for Booking Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1180598494/********.7+Validate+fare+for+booking">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ******** - Store Fare Breakdown Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1181713304/********.8+Store+Fare+Breakdown+Use+Case+Design">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ******** - Fare Upload Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1204617332/********.9+Fare+Upload+Use+Case+Design+Document">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ********0 - Get MultiFare in Cache by FareId Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1218609153/********.10+Get+Multi+Fare+Data+In+Cache+By+FareId+Use+Case+Design">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ********1 - Search Fare Breakdown Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1218609192/********.11+Search+Fare+Breakdown+Use+Case+Design">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ********2 - Get Location Surcharge Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1330250338/********.12+Get+Location+Surcharge+Use+Case+Design">Confluence</a></li>
    </ul>
  </li>
  <li>Reference: ********3 - Get Book-a-Ride Configuration Use Case
    <ul>
      <li><a href="https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1197867030/********.13+Get+book-a-ride+configs">Confluence</a></li>
    </ul>
  </li>
</ul>

## 4. Developer Guide
<a name="4-developer-guide"></a>

### 4.1. Folder Structure
<a name="41-folder-structure"></a>
```
ngp-me-dynamicpricing-svc/
- application/             ## Hexagonal architecture. Acts as bridge between domain layer and outer layers. Handles application-specific logic, such as use-case implementation.
- codebuild/               ## Contains AWS codebuild yaml files
- deployment/              ## Contains files which allow you to do deployment. docker-compose, kubernetes etc.
- docs/                    ## Assets (images/drawio) required for documentation.
- domain/                  ## Hexagonal architecture. Contains business logic and domain models.
- gradle/                  ## Contains gradle binaries. In most cases, do not touch.
- techframework/           ## Hexagonal architecture. Deals with extern concerns like databases, file systems and web services.
```
Developers are encouraged to read up on the hexagonal architecture concept, and to ensure that classes are put into the
appropriate modules.

Additional information can be found at the following links:
- [CDG Confluence - Hexagonal - Best Practices](https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1235257989/Hexagonal+Architecture+-+Best+Practices)


### 4.2. Running Locally
<a name="4.2-running-locally"></a>
Local deployment can be performed via the use of docker-compose
The main docker-compose file for the system is located in `deployment/docker-compose` and consists of the following:

```
deployment/
  - docker-compose/
    - docker-compose.yml
```

Unlike kubernetes, docker does not support concepts such as ingress nor load-balancing.
As such, there is a need for manual management of port mapping within the host machines.

The current port mappings are as follows:

| S/N | Service              | Description                   | Host Machine Port           | Container Port            | Profile    |
|-----|----------------------|-------------------------------|-----------------------------|---------------------------|------------|
| 1   | postgres             | Postgresql Database           | 15700                       | 5432                      | postgres   |
| 2   | redis                | Redis Database                | 15701                       | 6379                      | redis      |
| 3   | kafka-ui             | Management Portal for Kafka   | 15702                       | 8080                      | kafka      |
| 4   | kafka-zookeeper      | State Management DB for Kafka | 15703                       | 2181                      | kafka      |
| 5   | kafka                | Kafka Databus                 | 15704<br/> 15705<br/> 15706 | 9092<br/> 9997<br/> 29092 | kafka      |
| 6   | kafka-schemaregistry | Kafka Schema Registry         | 15707                       | 8085                      | kafka      |


Note that each service has been tagged with a docker-compose profile.   
For example, do run just the postgres services, you can execute the following command:

<u>Windows:</u>
```
docker-compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile postgres up -d
```

<u>Linux:</u>
```
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile postgres up -d
```

To run all services, you can just run with the profile all  argument:

<u>Windows:</u>
```
docker-compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d
```

<u>Linux:</u>
```
docker compose -p dynamicpricing-svc -f deployment/docker-compose/docker-compose.yml --profile all up -d
```

## 5. Scheduled Jobs
<a name="5-scheduled-jobs"></a>

This section provides an overview of all scheduled jobs (cron jobs) running in the Dynamic Pricing Service. These jobs perform automated maintenance tasks to ensure optimal system performance and data management.

### Job Summary

The application currently runs **2 scheduled jobs**:

| Job # | Service | Method | Schedule Type | Schedule Expression | Timezone | Description |
|-------|---------|---------|---------------|-------------------|----------|-------------|
| 1 | `RequestCounterServiceAdapter` | `syncLocalDataToRedis()` | Fixed Rate | Every 30 seconds | System Default | Synchronizes local request cache data to Redis for high-performance data access |
| 2 | `PartitionManagementService` | `scheduledPartitionMaintenance()` | Cron Expression | `0 0 1 * * ?` (Daily at 1:00 AM) | Asia/Singapore | Maintains database partitions specifically for the `surge_computation_model_api_logs` table |

### Detailed Job Information

#### 1. Request Counter Data Sync Job
- **Service**: `RequestCounterServiceAdapter`
- **Method**: `syncLocalDataToRedis()`
- **Schedule**: `@Scheduled(fixedRate = 30000)` (30 seconds)
- **Purpose**: Batch sync of local request cache data to Redis
- **Functionality**:
  - Collects local cached request count data
  - Performs batch Redis operations for optimal performance
  - Cleans up expired data automatically
  - Reduces network roundtrips by batching operations
- **Impact**: Critical for real-time surge computation performance
- **Configuration**: Fixed interval of 30 seconds (defined by `BATCH_SYNC_INTERVAL` constant)

#### 2. Database Partition Maintenance Job
- **Service**: `PartitionManagementService`
- **Method**: `scheduledPartitionMaintenance()`
- **Schedule**: `@Scheduled(cron = "#{@partitionConfiguration.maintenanceCron}", zone = "#{@partitionConfiguration.maintenanceTimezone}")`
- **Default Schedule**: Daily at 1:00 AM Singapore Time (`0 0 1 * * ?`)
- **Target Table**: `surge_computation_model_api_logs`
- **Purpose**: Automated database partition management specifically for surge computation API logs
- **Functionality**:
  - Creates future monthly partitions (configurable months ahead)
  - Optionally cleans up old partitions (if enabled)
  - Ensures optimal database performance for time-series data
  - Targets the `surge_computation_model_api_logs` table specifically
- **Configuration**: Fully configurable via `PartitionConfiguration` properties:
  - `app.partition.maintenance-cron`: Cron expression (default: `"0 0 1 * * ?"`)
  - `app.partition.maintenance-timezone`: Timezone (default: `"Asia/Singapore"`)
  - `app.partition.enable-scheduled-maintenance`: Enable/disable flag (default: `true`)
  - `app.partition.scheduled-months-ahead`: Future partitions to create (default: `6`)
  - `app.partition.enable-cleanup`: Enable old partition cleanup (default: `false`)

### Monitoring and Management

Both scheduled jobs include comprehensive logging and error handling:
- **Success/failure logging** with detailed metrics
- **Exception handling** to prevent application disruption
- **Configurable behavior** through application properties
- **Manual execution capabilities** for emergency situations

### Configuration Notes

- All scheduled jobs can be monitored through application logs
- Job configurations can be adjusted via application properties without code changes
- The partition maintenance job includes safety features to prevent accidental data loss
- Both jobs are designed to be non-blocking to application startup

## 6. Configuration Parameters
<a name="6-configuration-parameters"></a>

### 6.1. AWS Secrets Manager
<a name="6.1-aws-secrets-manager"></a>

/secrets/ngp/prod/ngp-me-r2dynamicpricing-svc:

| S/N | Name                                                              | Description                            |
|-----|-------------------------------------------------------------------|----------------------------------------|
| 1   | spring.datasource.url                                             | Postgres Url                           |
| 2   | spring.datasource.username                                        | Postgres Username                      |
| 3   | spring.datasource.password                                        | Postgres Password                      |
| 4   | liquibase.username                                                | Liquibase Postgres Username            |
| 5   | liquibase.password                                                | Liquibase Postgres Password            |
| 6   | spring.data.redis.host                                            | Redis Host                             |
| 7   | spring.data.redis.port                                            | Redis Port                             |
| 8   | spring.data.redis.username                                        | Redis Username                         |
| 9   | spring.data.redis.password                                        | Redis Password                         |
| 10  | spring.data.redis.ssl.enabled                                     | Redis SSL Enabled/Disabled             |
| 11  | spring.kafka.bootstrap-servers                                    | Kafka Bootstrap Servers                |
| 12  | spring.kafka.properties.sasl.jaas.config                          | Kafka Username and password            |
| 13  | spring.kafka.properties.sasl.mechanism                            | Kafka SASL Mechanism (eg. PLAIN)       |
| 14  | spring.kafka.properties.security.protocol                         | Kafka Security Protocol (eg. SASL_SSL) |
| 15  | spring.cloud.openfeign.client.config.addressClient.url            | Url for me-address-svc                 |
| 16  | spring.cloud.openfeign.client.config.fareClient.url               | Url for me-fare-svc                    |
| 17  | spring.cloud.openfeign.client.config.fleetAnalyticClient.url      | Url for me-fleetanalytic-svc           |
| 18  | spring.cloud.openfeign.client.config.cmsClient.url                | Url for common-cms-svc                 |
| 19 | spring.cloud.openfeign.client.config.weatherRetrievalClient.url    | Url for me-weather-retrieval-svc       |


### 6.2. AWS Parameter Store
<a name="6.2-aws-parameter-store"></a>

-- Not Applicable --

| S/N | Name                                                                                                  | Description                                                                                    | Type         |
|-----|-------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------|--------------|
| 1   | /config/ngp-me-r2dynamicpricing-svc_prod/spring/cloud/openfeign/client/config/addressClient/url       | ME Address Service URL. (eg. https://mob-me-address-app.apps.sg.{ENV}.zig.systems              | SecureString |
| 2   | /config/ngp-me-r2dynamicpricing-svc_prod/spring/cloud/openfeign/client/config/cmsClient/url           | Common CMS Service URL. (eg. https://mob-common-cms-app.apps.sg.{ENV}.zig.systems              | SecureString |
| 3   | /config/ngp-me-r2dynamicpricing-svc_prod/spring/cloud/openfeign/client/config/fareClient/url          | ME Fare Service URL. (eg. https://mob-me-fare-app.apps.sg.{ENV}.zig.systems                    | SecureString |
| 4   | /config/ngp-me-r2dynamicpricing-svc_prod/spring/cloud/openfeign/client/config/fleetAnalyticClient/url | ME Fleet Analytic Service URL. (eg. https://mob-me-fleetanalytic-app.apps.sg.{ENV}.zig.systems | SecureString |

### 6.3. NGP Configuration Management Service
<a name="6.3-ngp-configuration-management-service"></a>

<b>Note:</b>For unknown reason, the configuration parameters in CMS is stored under the service name 'me-pricing'

| S/N | Key | Value | Description |
| --- | --- | ----- | ----------- |
| 1 | `dosExcludeBookingChannels` | `GOJEK` | dosExcludeBookingChannels |
| 2 | `dosIncludeJobStatuses` | `FAILED,CANCELLED,CANCELLED_UR,NTA` | dosIncludeJobStatuses |
| 3 | `fleetAnalyticDemand15` | `{"demand_15": {"from": 2,"to": 12, "job_types": ["IMMEDIATE"]},"demand_15_pre": { "from": 7,"to": 17, "job_types": ["IMMEDIATE"]}}` | fleetAnalyticDemand15 |
| 4 | `fleetAnalyticDemand30` | `30` | fleetAnalyticDemand30 |
| 5 | `fleetAnalyticDemand60` | `60` | fleetAnalyticDemand60 |
| 6 | `newPricingModelConfig.items[0]` | `{"id":9630,"index":0,"zoneId":"01","startDt":"2024-02-01T03:36:15Z","endDt":"2024-12-31T15:59:59Z","additionalSurgeHigh":100,"surgeHighTierRate":0.6,"unmetRate1":0.3,"unmetRate2":0.68,"negativeDemandSupplyDownRate":0.9,"createdBy":"mya","createdDt":null,"updatedBy":"SYSTEM","updatedDt":"2024-09-11T07:43:24.879245139Z","k1":null,"k2":null,"k3":null,"k4":null,"k5":null,"k6":null,"k7":null,"k8":null,"zonePriceVersion":"V2.5"}` | -0-new-pricing-config |
| 7 | `newPricingModelConfig.items[1]` | `{ "id":2114, "index":1, "zoneId":"02", "startDt":"2023-12-01T20:00:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":50, "surgeHighTierRate":0.18, "unmetRate1":0.05, "unmetRate2":0.45, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2.5"  }` | -1-new-pricing-config |
| 8 | `newPricingModelConfig.items[10]` | `{ "id":2374, "index":10, "zoneId":"11", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -10-new-pricing-config |
| 9 | `newPricingModelConfig.items[11]` | `{ "id":2381, "index":11, "zoneId":"12", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -11-new-pricing-config |
| 10 | `newPricingModelConfig.items[12]` | `{ "id":2519, "index":12, "zoneId":"13", "startDt":"2024-02-29T13:43:50+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -12-new-pricing-config |
| 11 | `newPricingModelConfig.items[13]` | `{ "id":2760, "index":13, "zoneId":"14", "startDt":"2024-05-10T11:48:12+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -13-new-pricing-config |
| 12 | `newPricingModelConfig.items[14]` | `{ "id":2754, "index":14, "zoneId":"15", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -14-new-pricing-config |
| 13 | `newPricingModelConfig.items[15]` | `{ "id":2383, "index":15, "zoneId":"16", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -15-new-pricing-config |
| 14 | `newPricingModelConfig.items[16]` | `{ "id":2375, "index":16, "zoneId":"17", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -16-new-pricing-config |
| 15 | `newPricingModelConfig.items[17]` | `{ "id":2371, "index":17, "zoneId":"18", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":50, "surgeHighTierRate":0.4, "unmetRate1":0.5, "unmetRate2":0.8, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -17-new-pricing-config |
| 16 | `newPricingModelConfig.items[18]` | `{ "id":2379, "index":18, "zoneId":"19", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -18-new-pricing-config |
| 17 | `newPricingModelConfig.items[19]` | `{ "id":2376, "index":19, "zoneId":"20", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -19-new-pricing-config |
| 18 | `newPricingModelConfig.items[2]` | `{ "id":2369, "index":2, "zoneId":"03", "startDt":"2024-02-01T11:36:15+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -2-new-pricing-config |
| 19 | `newPricingModelConfig.items[20]` | `{ "id":2558, "index":20, "zoneId":"21", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -20-new-pricing-config |
| 20 | `newPricingModelConfig.items[21]` | `{ "id":2377, "index":21, "zoneId":"22", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -21-new-pricing-config |
| 21 | `newPricingModelConfig.items[22]` | `{ "id":2372, "index":22, "zoneId":"23", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -22-new-pricing-config |
| 22 | `newPricingModelConfig.items[23]` | `{ "id":2380, "index":23, "zoneId":"24", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -23-new-pricing-config |
| 23 | `newPricingModelConfig.items[24]` | `{ "id":2384, "index":24, "zoneId":"25", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -24-new-pricing-config |
| 24 | `newPricingModelConfig.items[25]` | `{ "id":2559, "index":25, "zoneId":"26", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -25-new-pricing-config |
| 25 | `newPricingModelConfig.items[26]` | `{ "id":2385, "index":26, "zoneId":"27", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -26-new-pricing-config |
| 26 | `newPricingModelConfig.items[27]` | `{ "id":2560, "index":27, "zoneId":"28", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -27-new-pricing-config |
| 27 | `newPricingModelConfig.items[28]` | `{ "id":2561, "index":28, "zoneId":"29", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -28-new-pricing-config |
| 28 | `newPricingModelConfig.items[29]` | `{ "id":2562, "index":29, "zoneId":"30", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -29-new-pricing-config |
| 29 | `newPricingModelConfig.items[3]` | `{ "id":2731, "index":3, "zoneId":"04", "startDt":"2024-05-06T16:11:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -3-new-pricing-config |
| 30 | `newPricingModelConfig.items[30]` | `{ "id":2751, "index":30, "zoneId":"31", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -30-new-pricing-config |
| 31 | `newPricingModelConfig.items[31]` | `{ "id":2752, "index":31, "zoneId":"32", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -31-new-pricing-config |
| 32 | `newPricingModelConfig.items[32]` | `{ "id":2563, "index":32, "zoneId":"33", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -32-new-pricing-config |
| 33 | `newPricingModelConfig.items[33]` | `{ "id":2564, "index":33, "zoneId":"34", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -33-new-pricing-config |
| 34 | `newPricingModelConfig.items[34]` | `{ "id":2565, "index":34, "zoneId":"35", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -34-new-pricing-config |
| 35 | `newPricingModelConfig.items[35]` | `{ "id":2566, "index":35, "zoneId":"36", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -35-new-pricing-config |
| 36 | `newPricingModelConfig.items[36]` | `{ "id":2755, "index":36, "zoneId":"37", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -36-new-pricing-config |
| 37 | `newPricingModelConfig.items[37]` | `{ "id":2758, "index":37, "zoneId":"38", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -37-new-pricing-config |
| 38 | `newPricingModelConfig.items[38]` | `{ "id":2328, "index":38, "zoneId":"39", "startDt":"2024-01-18T18:14:45+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.25, "unmetRate2":0.5, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -38-new-pricing-config |
| 39 | `newPricingModelConfig.items[39]` | `{ "id":2756, "index":39, "zoneId":"40", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -39-new-pricing-config |
| 40 | `newPricingModelConfig.items[4]` | `{ "id":2378, "index":4, "zoneId":"05", "startDt":"2024-02-01T11:36:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -4-new-pricing-config |
| 41 | `newPricingModelConfig.items[40]` | `{ "id":2569, "index":40, "zoneId":"41", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.45, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -40-new-pricing-config |
| 42 | `newPricingModelConfig.items[41]` | `{ "id":2753, "index":41, "zoneId":"42", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -41-new-pricing-config |
| 43 | `newPricingModelConfig.items[42]` | `{ "id":2571, "index":42, "zoneId":"43", "startDt":"2024-04-08T17:16:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.45, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -42-new-pricing-config |
| 44 | `newPricingModelConfig.items[43]` | `{ "id":2572, "index":43, "zoneId":"44", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.45, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -43-new-pricing-config |
| 45 | `newPricingModelConfig.items[44]` | `{ "id":2573, "index":44, "zoneId":"45", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.45, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -44-new-pricing-config |
| 46 | `newPricingModelConfig.items[45]` | `{ "id":2732, "index":45, "zoneId":"46", "startDt":"2024-05-06T16:11:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -45-new-pricing-config |
| 47 | `newPricingModelConfig.items[46]` | `{ "id":2574, "index":46, "zoneId":"47", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -46-new-pricing-config |
| 48 | `newPricingModelConfig.items[47]` | `{ "id":2575, "index":47, "zoneId":"48", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -47-new-pricing-config |
| 49 | `newPricingModelConfig.items[48]` | `{ "id":2576, "index":48, "zoneId":"49", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -48-new-pricing-config |
| 50 | `newPricingModelConfig.items[49]` | `{ "id":2577, "index":49, "zoneId":"50", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -49-new-pricing-config |
| 51 | `newPricingModelConfig.items[5]` | `{ "id":2516, "index":5, "zoneId":"06", "startDt":"2024-02-29T13:43:50+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -5-new-pricing-config |
| 52 | `newPricingModelConfig.items[50]` | `{ "id":2391, "index":50, "zoneId":"51", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -50-new-pricing-config |
| 53 | `newPricingModelConfig.items[51]` | `{ "id":2536, "index":51, "zoneId":"52", "startDt":"2024-03-22T10:41:54+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -51-new-pricing-config |
| 54 | `newPricingModelConfig.items[52]` | `{ "id":2733, "index":52, "zoneId":"53", "startDt":"2024-05-06T16:11:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -52-new-pricing-config |
| 55 | `newPricingModelConfig.items[53]` | `{ "id":2596, "index":53, "zoneId":"54", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -53-new-pricing-config |
| 56 | `newPricingModelConfig.items[54]` | `{ "id":2759, "index":54, "zoneId":"55", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -54-new-pricing-config |
| 57 | `newPricingModelConfig.items[55]` | `{ "id":2394, "index":55, "zoneId":"56", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -55-new-pricing-config |
| 58 | `newPricingModelConfig.items[56]` | `{ "id":2395, "index":56, "zoneId":"57", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -56-new-pricing-config |
| 59 | `newPricingModelConfig.items[57]` | `{ "id":2578, "index":57, "zoneId":"58", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -57-new-pricing-config |
| 60 | `newPricingModelConfig.items[58]` | `{ "id":2579, "index":58, "zoneId":"59", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -58-new-pricing-config |
| 61 | `newPricingModelConfig.items[59]` | `{ "id":2734, "index":59, "zoneId":"60", "startDt":"2024-05-06T16:11:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -59-new-pricing-config |
| 62 | `newPricingModelConfig.items[6]` | `{ "id":2517, "index":6, "zoneId":"07", "startDt":"2024-02-29T13:43:50+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -6-new-pricing-config |
| 63 | `newPricingModelConfig.items[60]` | `{ "id":2580, "index":60, "zoneId":"61", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -60-new-pricing-config |
| 64 | `newPricingModelConfig.items[61]` | `{ "id":2581, "index":61, "zoneId":"62", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -61-new-pricing-config |
| 65 | `newPricingModelConfig.items[62]` | `{ "id":2397, "index":62, "zoneId":"63", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -62-new-pricing-config |
| 66 | `newPricingModelConfig.items[63]` | `{ "id":2398, "index":63, "zoneId":"64", "startDt":"2024-02-01T11:36:17+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -63-new-pricing-config |
| 67 | `newPricingModelConfig.items[64]` | `{ "id":2399, "index":64, "zoneId":"65", "startDt":"2024-02-01T11:36:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -64-new-pricing-config |
| 68 | `newPricingModelConfig.items[65]` | `{ "id":2582, "index":65, "zoneId":"66", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -65-new-pricing-config |
| 69 | `newPricingModelConfig.items[66]` | `{ "id":2597, "index":66, "zoneId":"67", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -66-new-pricing-config |
| 70 | `newPricingModelConfig.items[67]` | `{ "id":2735, "index":67, "zoneId":"68", "startDt":"2024-05-06T16:11:01+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -67-new-pricing-config |
| 71 | `newPricingModelConfig.items[68]` | `{ "id":2583, "index":68, "zoneId":"69", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -68-new-pricing-config |
| 72 | `newPricingModelConfig.items[69]` | `{ "id":2584, "index":69, "zoneId":"70", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -69-new-pricing-config |
| 73 | `newPricingModelConfig.items[7]` | `{ "id":2518, "index":7, "zoneId":"08", "startDt":"2024-02-29T13:43:50+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.6, "unmetRate1":0.3, "unmetRate2":0.68, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -7-new-pricing-config |
| 74 | `newPricingModelConfig.items[70]` | `{ "id":2585, "index":70, "zoneId":"71", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -70-new-pricing-config |
| 75 | `newPricingModelConfig.items[71]` | `{ "id":2586, "index":71, "zoneId":"72", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -71-new-pricing-config |
| 76 | `newPricingModelConfig.items[72]` | `{ "id":2587, "index":72, "zoneId":"73", "startDt":"2024-04-08T17:16:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -72-new-pricing-config |
| 77 | `newPricingModelConfig.items[73]` | `{ "id":2402, "index":73, "zoneId":"74", "startDt":"2024-02-01T11:36:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -73-new-pricing-config |
| 78 | `newPricingModelConfig.items[74]` | `{ "id":2588, "index":74, "zoneId":"75", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -74-new-pricing-config |
| 79 | `newPricingModelConfig.items[75]` | `{ "id":2403, "index":75, "zoneId":"76", "startDt":"2024-02-01T11:36:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -75-new-pricing-config |
| 80 | `newPricingModelConfig.items[76]` | `{ "id":2589, "index":76, "zoneId":"77", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -76-new-pricing-config |
| 81 | `newPricingModelConfig.items[77]` | `{ "id":2590, "index":77, "zoneId":"78", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -77-new-pricing-config |
| 82 | `newPricingModelConfig.items[78]` | `{ "id":2404, "index":78, "zoneId":"79", "startDt":"2024-02-01T11:36:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -78-new-pricing-config |
| 83 | `newPricingModelConfig.items[79]` | `{ "id":2591, "index":79, "zoneId":"80", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -79-new-pricing-config |
| 84 | `newPricingModelConfig.items[8]` | `{"id":9638,"index":8,"zoneId":"09","startDt":"2024-02-01T03:36:16Z","endDt":"2024-12-31T15:59:59Z","additionalSurgeHigh":100,"surgeHighTierRate":0.6,"unmetRate1":0.3,"unmetRate2":0.68,"negativeDemandSupplyDownRate":0.9,"createdBy":"mya","createdDt":null,"updatedBy":"SYSTEM","updatedDt":"2024-09-11T07:52:42.944962343Z","k1":null,"k2":null,"k3":null,"k4":null,"k5":null,"k6":null,"k7":null,"k8":null,"zonePriceVersion":"V2.5"}` | -8-new-pricing-config |
| 85 | `newPricingModelConfig.items[80]` | `{ "id":2592, "index":80, "zoneId":"81", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -80-new-pricing-config |
| 86 | `newPricingModelConfig.items[81]` | `{ "id":2757, "index":81, "zoneId":"82", "startDt":"2024-05-10T11:48:11+08:00", "endDt":"2025-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0.0071, "k2":2.3, "k3":50, "k4":0.25, "k5":380.05, "k6":2.94, "k7":5.71, "k8":0, "zonePriceVersion":"V3"  }` | -81-new-pricing-config |
| 87 | `newPricingModelConfig.items[82]` | `{ "id":2593, "index":82, "zoneId":"90", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -82-new-pricing-config |
| 88 | `newPricingModelConfig.items[83]` | `{ "id":2594, "index":83, "zoneId":"93", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -83-new-pricing-config |
| 89 | `newPricingModelConfig.items[84]` | `{ "id":2406, "index":84, "zoneId":"95", "startDt":"2024-02-01T11:36:18+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-03-26T10:24:25+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -84-new-pricing-config |
| 90 | `newPricingModelConfig.items[85]` | `{ "id":2595, "index":85, "zoneId":"97", "startDt":"2024-04-08T17:16:19+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -85-new-pricing-config |
| 91 | `newPricingModelConfig.items[86]` | `{ "id":2199, "index":86, "zoneId":"99", "startDt":"2023-12-01T20:00:00+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":50, "surgeHighTierRate":0.18, "unmetRate1":0.05, "unmetRate2":0.45, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"mya", "updatedDt":"2024-04-08T17:16:16+08:00", "k1":null, "k2":null, "k3":null, "k4":null, "k5":null, "k6":null, "k7":null, "k8":null, "zonePriceVersion":"V2"  }` | -86-new-pricing-config |
| 92 | `newPricingModelConfig.items[9]` | `{ "id":2556, "index":9, "zoneId":"10", "startDt":"2024-04-08T17:16:16+08:00", "endDt":"2024-12-31T23:59:59+08:00", "additionalSurgeHigh":100, "surgeHighTierRate":0.5, "unmetRate1":0.3, "unmetRate2":0.75, "negativeDemandSupplyDownRate":0.9, "createdBy":"mya", "updatedBy":"", "updatedDt":null, "k1":0, "k2":0, "k3":0, "k4":0, "k5":0, "k6":0, "k7":0, "k8":0, "zonePriceVersion":"V2"  }` | -9-new-pricing-config |
| 93 | `std001Weight` | `0.3` | std001Weight |

## 6. CI/CD
<a name="ci-cd"></a>
--To be populated soon--

## 7. Swagger Links
<a name="swagger-links"></a>
--To be populated soon--

## 8. Glossary
<a name="glossary"></a>
--To be populated soon--

## 9. Appendix
<a name="appendix"></a>
--To be populated soon--


