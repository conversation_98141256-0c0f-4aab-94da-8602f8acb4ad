import org.gradle.api.GradleException
import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

plugins {
  java
  jacoco
  id("org.sonarqube") version "5.0.0.4638"
  id("org.liquibase.gradle") version "2.2.1"
  id("com.google.cloud.artifactregistry.gradle-plugin") version "2.1.5"
  id("org.springdoc.openapi-gradle-plugin") version "1.8.0"
  id("org.openapi.generator") version "7.4.0"
  id("org.springframework.boot") version "3.1.3"
  id("io.spring.dependency-management") version "1.0.15.RELEASE"
  id("jacoco-report-aggregation")
  id("com.gorylenko.gradle-git-properties") version "2.4.1"
  id("com.github.johnrengelman.shadow") version "7.1.2"
  id("com.google.protobuf") version "0.9.4"
}

ext { set("springCloudVersion", "2022.0.2") }

group = "com.cdg.pmg.ngp.me.dynamicpricing.techframework"

version = "0.0.1-SNAPSHOT"

java.sourceCompatibility = JavaVersion.VERSION_17

jacoco { toolVersion = "0.8.8" }

repositories {
  mavenCentral()
  mavenLocal()
}

dependencies {
  implementation(project(":application"))
  implementation(project(":domain"))

  implementation(libs.spring.boot.starter.web)
  implementation(libs.spring.boot.starter.validation)
  implementation(libs.spring.boot.configuration.processor)
  implementation(libs.spring.cloud.starter.config)
  implementation(libs.spring.cloud.starter.bus.kafka)

  // AWS Parameter Store for externalized configuration
  implementation(libs.spring.cloud.aws.starter)
  implementation(libs.spring.cloud.aws.starter.parameter.store)
  implementation(libs.spring.cloud.aws.starter.secrets.manager)

  // Jakarta Servlet API - Since from Spring Boot 3, javax namespace was changed to jakarta
  implementation(libs.jakarta.servlet.api)
  implementation(libs.jakarta.persistence.api)

  // Database implementation
  implementation(libs.spring.boot.starter.data.jpa)
  implementation(libs.postgresql)

  // Database Version Control with Liquibase
  implementation(libs.liquibase.core)
  liquibaseRuntime(libs.liquibase.core)
  liquibaseRuntime(libs.liquibase.groovy.dsl)
  liquibaseRuntime(libs.picocli)
  liquibaseRuntime(libs.postgresql)

  // Actuator, metric, logging and tracing
  implementation(libs.spring.boot.starter.actuator)
  implementation(libs.micrometer.registry.prometheus)
  implementation(libs.micrometer.core)
  implementation(libs.micrometer.tracing.bridge.otel)
  implementation(libs.logbook.spring.boot.starter)

  implementation(libs.logback.json.classic)
  implementation(libs.logback.jackson)

  implementation(libs.git.commit.id.maven.plugin)

  // lombok compile and processor
  compileOnly(libs.lombok)
  annotationProcessor(libs.lombok)

  // Mapstruct
  implementation(libs.mapstruct)
  annotationProcessor(libs.mapstruct.processor)
  annotationProcessor(libs.lombok.mapstruct.binding)
  annotationProcessor(libs.mapstruct.processor)

  // Redis cache
  implementation(libs.spring.data.redis)
  implementation(libs.lettuce.core)

  // OpenAPI
  implementation(libs.springdoc.openapi.starter.webmvc.ui)

  // Runtime library
  implementation(libs.therapi.runtime.javadoc)
  annotationProcessor(libs.therapi.runtime.javadoc)

  // FeignClient for HTTP Integration
  implementation(libs.spring.cloud.starter.openfeign)
  implementation(libs.feign.micrometer)

  // Integration for upload file by SFTP
  implementation(libs.spring.boot.starter.integration)
  implementation(libs.spring.integration.sftp)
  implementation(libs.spring.integration.file)
  testImplementation(libs.spring.integration.test)

  // protobuf implementation
  implementation(libs.protobuf.java)

  // Test Implementation
  testImplementation(libs.spring.boot.starter.test) {
    exclude(group = "com.vaadin.external.google", module = "android-json")
  }
  testImplementation(libs.testcontainers)
  testImplementation(libs.testcontainers.junit.jupiter)
  testImplementation(libs.testcontainers.postgresql)
  testImplementation(libs.testcontainers.kafka)
  testImplementation(libs.testcontainers.wiremock)
  testImplementation(libs.retrofit)
  testImplementation(libs.retrofit.jackson)
  testImplementation(libs.testcontainers.redis)
}

// =============== GitProperties Settings ===============
configure<com.gorylenko.GitPropertiesPluginExtension> {
  failOnNoGitDirectory = false
  (dotGitDirectory as DirectoryProperty).set(file("${project.rootDir}/.git"))
  gitPropertiesName = "git.properties"
  extProperty = "gitProps"
  (gitPropertiesResourceDir as DirectoryProperty).set(
      file("${project.layout.buildDirectory.get()}/git.properties"))
}

tasks.generateGitProperties { outputs.upToDateWhen { false } }

// ================ Liquibase Settings =================
configure<org.liquibase.gradle.LiquibaseExtension> {
  activities.register("main") {
    this.arguments =
        mapOf(
            "searchPath" to "${project.rootDir}/techframework/src/main/resources/",
            "logLevel" to "debug",
            "changelogFile" to "db/db.changelog-master.xml",
            "url" to project.property("url"),
            "username" to project.property("username"),
            "password" to project.property("password"))
  }
}

// ================= Resources Settings ================
tasks.processResources {
  mustRunAfter(tasks.generateGitProperties)
  dependsOn(tasks.generateGitProperties)
  outputs.upToDateWhen { false }
}

// ================= Jar Settings ================
tasks.withType<com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar> {
  archiveClassifier.set("") // Empty to replace the regular jar
  manifest {
    attributes(
        "Main-Class" to
            "com.cdg.pmg.ngp.me.dynamicpricing.techframework.TechFrameworkApplication") // Specify
    // your main
    // class
    // here
  }
}

// ================= OpenAPI Settings ================
// tasks to generated REST interfaces based on OpenAPI yaml specification
var openApiTypeMappings = mapOf("time" to "LocalTime")

tasks.openApiGenerate {
  generatorName.set("spring")
  inputSpec.set("$projectDir/src/main/resources/openapi/api.yaml")
  outputDir.set("${project.layout.buildDirectory.get()}/generated/openapi")
  apiPackage.set("com.cdg.pmg.ngp.me.dynamicpricing.techframework.api")
  modelPackage.set("com.cdg.pmg.ngp.me.dynamicpricing.techframework.model")
  importMappings.set(mapOf("LocalTime" to "java.time.LocalTime"))
  generateApiTests.set(false)
  generateModelTests.set(false)
  generateModelDocumentation.set(false)
  configOptions.set(
      mapOf(
          "dateLibrary" to "java8",
          "generateApis" to "true",
          "generateApiTests" to "false",
          "generateModels" to "true",
          "generateModelTests" to "false",
          "generateModelDocumentation" to "false",
          "generateSupportingFiles" to "false",
          "hideGenerationTimestamp" to "true",
          "interfaceOnly" to "true",
          "library" to "spring-boot",
          "serializableModel" to "true",
          "useBeanValidation" to "true",
          "useTags" to "true",
          "implicitHeaders" to "true",
          "openApiNullable" to "false",
          "oas3" to "true",
          "useSpringBoot3" to "true"))

  typeMappings.set(openApiTypeMappings)
}

// Task to add generated OpenAPI classes into sourceset
sourceSets {
  main {
    java { srcDirs("${project.layout.buildDirectory.get()}/../domain/src/main/java") }
    java { srcDirs("${project.layout.buildDirectory.get()}/../application/src/main/java") }
    java { srcDirs("${project.layout.buildDirectory.get()}/generated/openapi/src/main/java") }
  }
}

// Configure all JavaCompile tasks to depend on openApiGenerate and spotlessApply
tasks.withType<JavaCompile>().configureEach { dependsOn(tasks.openApiGenerate, "spotlessApply") }

// Ensure spotlessApply runs before compileJava
tasks.named("compileJava") { mustRunAfter("spotlessApply") }

// Ensure spotlessCheck runs during the check task
tasks.named("check") { dependsOn("spotlessCheck") }

// ================= Unit Test Settings ================
tasks.withType<Test> {
  useJUnitPlatform()
  testLogging {
    events(
        TestLogEvent.PASSED, TestLogEvent.FAILED, TestLogEvent.STANDARD_ERROR, TestLogEvent.SKIPPED)
    exceptionFormat = TestExceptionFormat.FULL
    showExceptions = true
    showCauses = true
    showStackTraces = true
  }
  reports {
    html.required.set(false)
    junitXml.required.set(true)
    junitXml.apply {
      mergeReruns.set(true) // defaults to false
    }
  }
  outputs.upToDateWhen { false }
  addTestListener(
      object : TestListener {
        override fun beforeSuite(suite: TestDescriptor) {}
        override fun beforeTest(testDescriptor: TestDescriptor) {}
        override fun afterTest(testDescriptor: TestDescriptor, result: TestResult) {}
        override fun afterSuite(suite: TestDescriptor, result: TestResult) {
          if (suite.parent == null) {
            println("----")
            println("Suite name: ${suite.name}")
            println("Test result: ${result.resultType}")
            println(
                "Test summary: ${result.testCount} tests, " +
                    "${result.successfulTestCount} succeeded, " +
                    "${result.failedTestCount} failed, " +
                    "${result.skippedTestCount} skipped")
          }
        }
      })
}

// ============================ Definition of Integration Test Type ============================
var integrationTest: SourceSet =
    sourceSets.create("integrationTest") {
      java {
        compileClasspath += sourceSets.main.get().output + sourceSets.test.get().output
        runtimeClasspath += sourceSets.main.get().output + sourceSets.test.get().output
        srcDir("src/integrationTest/java")
      }
      resources { srcDir("src/integrationTest/resources") }
    }

tasks.named<ProcessResources>("processIntegrationTestResources") {
  duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

configurations[integrationTest.implementationConfigurationName].extendsFrom(
    configurations.testImplementation.get())

configurations[integrationTest.implementationConfigurationName].extendsFrom(
    configurations.implementation.get())

configurations[integrationTest.compileOnlyConfigurationName].extendsFrom(
    configurations.compileOnly.get())

configurations[integrationTest.runtimeOnlyConfigurationName].extendsFrom(
    configurations.testRuntimeOnly.get())

configurations[integrationTest.annotationProcessorConfigurationName].extendsFrom(
    configurations.annotationProcessor.get())

val integrationTestTask =
    tasks.register<Test>("integrationTest") {
      group = "verification"
      description = "performs integration test for this project"
      useJUnitPlatform()
      reports { junitXml.required.set(true) }

      testClassesDirs = integrationTest.output.classesDirs
      classpath = sourceSets["integrationTest"].runtimeClasspath

      shouldRunAfter(tasks.test)
    }

// ================================= Jacoco Settings ========================================
// Configure Jacoco Test Coverage Report for project's unit test source set
tasks.jacocoTestReport {
  dependsOn(tasks.test) // tests are required to run before generating the report
  outputs.upToDateWhen { false }
  reports {
    xml.required.set(true)
    csv.required.set(false)
    xml.outputLocation.set(layout.buildDirectory.file("reports/jacoco/unit/jacocoTestReport.xml"))
    html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco/unit/html"))
  }
  sourceDirectories.setFrom(files(sourceSets["main"].allSource.srcDirs))
  classDirectories.setFrom(files(sourceSets["main"].output))
}

// configuration for integration test reporting. Note the configuration of unit testing is defined
// the project's root build.gradle.kts
val jacocoIntegrationTestReport by
    tasks.creating(JacocoReport::class) {
      dependsOn(integrationTestTask)
      reports {
        xml.required.set(true)
        html.required.set(true)
        xml.outputLocation.set(
            layout.buildDirectory.file("reports/jacoco/integration/jacocoTestReport.xml"))
        html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco/integration/html"))
      }
      sourceDirectories.setFrom(files(sourceSets["main"].allSource.srcDirs))
      classDirectories.setFrom(files(sourceSets["main"].output))

      // Define the output location for the integration test exec file
      executionData.setFrom(layout.buildDirectory.file("jacoco/integrationTest.exec"))
    }

// configuration for aggregate test reporting
val jacocoAggregateReport by
    tasks.creating(JacocoReport::class) {
      dependsOn(tasks.jacocoTestReport, jacocoIntegrationTestReport, ":domain:test")
      dependsOn(tasks.jacocoTestReport, jacocoIntegrationTestReport, ":application:test")
      reports {
        xml.required.set(true)
        html.required.set(true)
        xml.outputLocation.set(
            layout.buildDirectory.file("reports/jacoco/aggregate/jacocoTestReport.xml"))
        html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco/aggregate/html"))
      }
      sourceDirectories.setFrom(
          files(sourceSets["main"].allSource.srcDirs),
          file("../domain/src/main/java"),
          file("../application/src/main/java"))
      classDirectories.setFrom(files(sourceSets["main"].output))

      // Include execution data from both unit and integration tests
      // add exec files from unit tests
      val execFiles =
          files(
                  layout.buildDirectory.file("jacoco/test.exec"),
                  layout.buildDirectory.file("jacoco/integrationTest.exec"),
                  layout.projectDirectory.file("../domain/build/jacoco/test.exec"),
                  layout.projectDirectory.file("../application/build/jacoco/test.exec"))
              .filter { it.exists() }
      executionData.setFrom(execFiles)

      // include classes from application and domain dependencies
      val classDirectoriesToInclude =
          files(
              layout.projectDirectory.dir("../domain/build/classes/java/main"),
              layout.projectDirectory.dir("../application/build/classes/java/main"),
              fileTree(
                  mapOf(
                      "dir" to layout.buildDirectory.dir("classes/java/main"),
                      "excludes" to
                          listOf(
                              "**/entities/**",
                          ))))
      classDirectories.setFrom(classDirectoriesToInclude)
    }

tasks.check { dependsOn(jacocoAggregateReport) }

// ================== SonarQube Settings ==================
sonar {
  val coverageExcludeDirList =
      arrayListOf(
          "**/configs/**",
          "**/exceptions/**",
          "**/mappers/**",
          "**/mapper/**",
          "**/constant/**",
          "**/constants/**",
          "**/entities/**",
          "**/valueobjects/**",
          "**/dtos/**",
          "**/dto/**",
          "**/commands/**",
          "**/queries/**",
          "**/message/*",
          "**/enums/**",
          "**/models/**",
          "**/language/**",
          "**/model/**",
          "**/request/**",
          "**/response/**",
          "**/validator/**",
          "**/annotations/**",
          "**/restful/annotation/**",
          "**/TechFrameworkApplication.*",
          "**/test/**",
      )
  val duplicationExcludeDirList =
      arrayListOf(
          "**/configs/**",
          "**/exceptions/**",
          "**/mapper/**",
          "**/enums/**",
          "**/entities/**",
          "**/dtos/**",
          "**/models/**",
          "**/strategy/**")
  val coverageExcludeDir = coverageExcludeDirList.joinToString(",")
  val duplicationExcludeDir = duplicationExcludeDirList.joinToString(",")

  properties {
    // project identification
    property("sonar.projectName", "ngp-me-dynamicpricing-svc")
    property("sonar.projectVersion", "1.0")
    property("sonar.projectBaseDir", "$projectDir/..")

    // report paths and binaries
    property(
        "sonar.junit.reportPaths",
        "$projectDir/build/test-results/test, $projectDir/build/test-results/integrationTest")
    property(
        "sonar.java.test.binaries",
        "$projectDir/build/classes/java/integrationTest,$projectDir/build/classes/java/test")
    property("sonar.test", "$projectDir/src/test/java,$projectDir/src/integrationTest/java")
    property(
        "sonar.java.binaries",
        "$projectDir/build/classes/java/main,$projectDir/../domain/build/classes/java/main,$projectDir/../application/build/classes/java/main")
    property(
        "sonar.coverage.jacoco.xmlReportPaths",
        "$projectDir/build/reports/jacoco/aggregate/jacocoTestReport.xml")
    property(
        "sonar.sources",
        "$projectDir/src/main/java,$projectDir/../domain/src/main/java,$projectDir/../application/src/main/java")
    property(
        "sonar.binaries",
        "$projectDir/build//classes/java/main,$projectDir/../domain/build/classes/java/main,$projectDir/../application/build/classes/java/main")
    property("sonar.coverage.exclusions", coverageExcludeDir)
    property("sonar.cpd.exclusions", duplicationExcludeDir)
  }
}

// ============================ Misc Configuration ============================
dependencyManagement {
  imports {
    mavenBom("org.springframework.cloud:spring-cloud-dependencies:${ext.get("springCloudVersion")}")
  }
}

// ============================ Protobuf Configuration ============================
// val protoc = libs.protoc.get()
// protobuf { protoc { artifact = "$protoc" } }
protobuf { protoc { artifact = "com.google.protobuf:protoc:3.25.1" } }

// ============================ Docker Configuration ============================
// Create a task for building with the local Dockerfile
tasks.register("dockerBuildLocal") {
  description = "Builds the Docker image for local development using Docker Hub images"
  group = "docker"

  // Ensure build task is run first if it hasn't been run yet
  dependsOn(tasks.named("build"))

  doLast {
    // Check if the JAR file exists in the root project's build/libs folder
    val jarFile = file("${rootProject.buildDir}/libs/${rootProject.name}-${project.version}.jar")
    if (!jarFile.exists()) {
      logger.lifecycle("JAR file not found. Make sure to run the build task first.")
      throw GradleException(
          "JAR file not found at ${jarFile.absolutePath}. Please run './gradlew build' first.")
    }

    // Build the Docker image
    logger.lifecycle("Building Docker image using Dockerfile-local with 'local' Spring profile...")
    exec {
      workingDir = rootProject.projectDir
      commandLine =
          listOf(
              "docker",
              "build",
              "-t",
              "${project.group}/${rootProject.name}:latest",
              "-f",
              "Dockerfile-local",
              "--build-arg",
              "JAR_FILE=build/libs/${rootProject.name}-${project.version}.jar",
              ".")
    }

    logger.lifecycle(
        "Docker image built successfully with tag: ${project.group}/${rootProject.name}:latest (using 'local' Spring profile)")
  }
}
