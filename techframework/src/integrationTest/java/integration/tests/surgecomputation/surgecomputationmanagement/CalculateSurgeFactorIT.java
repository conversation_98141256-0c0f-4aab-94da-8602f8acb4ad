package integration.tests.surgecomputation.surgecomputationmanagement;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelApiLogRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.SurgeComputationModelSurgeRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelApiLogEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.SurgeComputationModelSurgeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CalculateSurgeFactorIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String MODEL_NAME = "test_v4_model";
  private static final String VERSION = "1.0";
  private static final long REGION_ID = 1L;

  private static final String TIME_BASED_SURGE_HIGH = "surge_high";
  private static final String TIME_BASED_SURGE_LOW = "surge_low";

  private static final String REGION_BASED_UNMET_RATE_1 = "unmet_rate_1";
  private static final String REGION_BASED_UNMET_RATE_2 = "unmet_rate_2";

  private static final String LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND = "ComfortRideDemand";
  private static final String LIVE_STANDARD_INPUT_METER_UNMET_DEMAND = "MeterUnmetDemand";

  private final SurgeFactorCalculationService surgeFactorCalculationService;
  private final SurgeComputationModelApiLogRepository surgeComputationModelApiLogRepository;
  private final SurgeComputationModelSurgeRepository surgeComputationModelSurgeRepository;

  /**
   * Don't change this url, if you want to change it, please make sure the new url is defined under
   * wiremock/mappings.
   */
  private static final String SURGE_COMPUTATION_MODEL_V4_URL = "/test/calculate_surge";

  @Test
  void testCalculateSurgeFactor_success() throws Exception {
    // Arrange
    Long modelId = setupModelData();
    setupStaticTimeBasedConfigurations();
    setupStaticRegionBasedConfigurations();

    // Act
    Response<Void> response = dynamicPricingServiceApi.calculateSurgeFactor().execute();

    // Wait for the async task to complete
    Thread.sleep(3000);

    assertTrue(response.isSuccessful());

    Map<Long, BigDecimal> h3RegionSurgeMap =
        surgeFactorCalculationService.getH3RegionSurgeMap(MODEL_NAME);
    assertEquals(3, h3RegionSurgeMap.size());
    assertEquals(BigDecimal.valueOf(6), h3RegionSurgeMap.get(REGION_ID));

    List<SurgeComputationModelApiLogEntity> apiLogs =
        surgeComputationModelApiLogRepository.findByModelId(modelId);
    assertEquals(1, apiLogs.size());
    assertEquals(200, apiLogs.get(0).getStatusCode());
    assertEquals(SURGE_COMPUTATION_MODEL_V4_URL, apiLogs.get(0).getEndpointUrl());

    List<SurgeComputationModelSurgeEntity> h3RegionSurgesFromDB =
        surgeComputationModelSurgeRepository.findByModelId(modelId);
    assertEquals(3, h3RegionSurgesFromDB.size());
    for (final SurgeComputationModelSurgeEntity surge : h3RegionSurgesFromDB) {
      assertNotNull(surge.getId());
      assertNotNull(surge.getLastUpdDt());
      if (surge.getRegionId() == REGION_ID) {
        assertEquals(BigDecimal.valueOf(6), surge.getSurge());
      } else if (surge.getRegionId() == 2L) {
        assertEquals(BigDecimal.valueOf(10), surge.getSurge());
      } else if (surge.getRegionId() == 3L) {
        assertEquals(BigDecimal.valueOf(50), surge.getSurge());
      }
    }
  }

  private void setupStaticRegionBasedConfigurations() throws Exception {
    OffsetDateTime now = OffsetDateTime.now();

    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName(REGION_BASED_UNMET_RATE_1);
    request.setVersion(VERSION);
    request.setEffectiveFrom(now.minusDays(1));
    request.setDescription(REGION_BASED_UNMET_RATE_1);
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(REGION_ID, "0.4")));

    StaticRegionBasedConfigurationRequest request2 = new StaticRegionBasedConfigurationRequest();
    request2.setName(REGION_BASED_UNMET_RATE_2);
    request2.setVersion(VERSION);
    request2.setEffectiveFrom(now.minusDays(1));
    request2.setDescription(REGION_BASED_UNMET_RATE_2);
    request2.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(REGION_ID, "0.75")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request, request2))
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
  }

  private void setupStaticTimeBasedConfigurations() throws Exception {
    OffsetDateTime now = OffsetDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE", Locale.ENGLISH);
    String dayOfWeek = now.format(formatter).toUpperCase();
    OffsetDateTime effectiveFrom = now.minusDays(1);

    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName(TIME_BASED_SURGE_HIGH);
    request.setVersion(VERSION);
    request.setEffectiveFrom(effectiveFrom);
    request.setDescription(TIME_BASED_SURGE_HIGH);

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.fromValue(
                    dayOfWeek),
                now.getHour(),
                "8.5"));

    request.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest request2 = new StaticTimeBasedConfigurationRequest();
    request2.setName(TIME_BASED_SURGE_LOW);
    request2.setVersion(VERSION);
    request2.setEffectiveFrom(effectiveFrom);
    request2.setDescription(TIME_BASED_SURGE_LOW);

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.fromValue(
                    dayOfWeek),
                now.getHour(),
                "1.5"));

    request2.setAppliedHours(appliedHours2);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request, request2), TEST_USER_ID)
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
  }

  private Long setupModelData() throws Exception {
    List<SurgeComputationModelRequestRequestFieldsMappingsInner> mappings =
        List.of(
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_high",
                TIME_BASED_SURGE_HIGH),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_TIME_BASED_CONFIGURATION,
                "v4_t_surge_low",
                TIME_BASED_SURGE_LOW),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_1",
                REGION_BASED_UNMET_RATE_1),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .STATIC_REGION_BASED_CONFIGURATION,
                "v4_c_unmet_rate_2",
                REGION_BASED_UNMET_RATE_2),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "comfortride_demand",
                LIVE_STANDARD_INPUT_COMFORTRIDE_DEMAND),
            new SurgeComputationModelRequestRequestFieldsMappingsInner(
                SurgeComputationModelRequestRequestFieldsMappingsInner.MappingTypeEnum
                    .LIVE_STANDARD_INPUT,
                "meter_unmet_demand",
                LIVE_STANDARD_INPUT_METER_UNMET_DEMAND));

    SurgeComputationModelRequest request = new SurgeComputationModelRequest();
    request.setModelName(MODEL_NAME);
    request.setDescription("Test description for surge model");
    request.setEndpointUrl(SURGE_COMPUTATION_MODEL_V4_URL);
    request.setRequestFieldsMappings(mappings);

    // Act
    Response<SurgeComputationModelResponse> response =
        dynamicPricingServiceApi.createSurgeComputationModel(request, TEST_USER_ID).execute();

    // Assert
    assertTrue(response.isSuccessful());
    Assertions.assertNotNull(response.body());

    return response.body().getData().getId();
  }
}
