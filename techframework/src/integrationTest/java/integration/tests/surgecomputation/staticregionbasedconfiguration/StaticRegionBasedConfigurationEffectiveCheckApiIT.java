package integration.tests.surgecomputation.staticregionbasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class StaticRegionBasedConfigurationEffectiveCheckApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String VERSION = "1.0";

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNull() throws IOException {
    createStaticRegionBasedConfiguration(OffsetDateTime.now(), null);

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNotNull() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now, now.plusDays(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withOutOfDate() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now.minusDays(1), now.minusSeconds(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withCloseToExpiration() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticRegionBasedConfiguration(now.minusDays(1), now.plusDays(1));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldWarning_whenNoEffectiveConfig_withNoAnyConfig() throws IOException {
    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticRegionBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertNull(checkResponse.body().getVersion());
    assertNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  private void createStaticRegionBasedConfiguration(
      final OffsetDateTime effectiveFrom, final OffsetDateTime effectiveTo) throws IOException {

    StaticRegionBasedConfigurationRequest request = new StaticRegionBasedConfigurationRequest();
    request.setName("Test Region Config");
    request.setVersion(VERSION);
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for region-based surge pricing");
    request.setRegionValues(
        List.of(new StaticRegionBasedConfigurationRequestRegionValuesInner(1L, "1.5")));

    // Act
    Response<StaticRegionBasedConfigurationBatchCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticRegionBasedConfiguration(TEST_USER_ID, List.of(request))
            .execute();

    assertTrue(response.isSuccessful());
  }
}
