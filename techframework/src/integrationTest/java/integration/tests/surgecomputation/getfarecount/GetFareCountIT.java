package integration.tests.surgecomputation.getfarecount;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.DynamicPricingRegionBasedService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.RegionModelDistributionService;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter.RequestCounterServiceAdapter;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetEstimatedFareInboundRequest;
import integration.IntegrationTestBase;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class GetFareCountIT extends IntegrationTestBase {

  private final RequestCounterServiceAdapter requestCounterService;
  @MockBean private DynamicPricingService dynamicPricingService;
  @MockBean private DynamicPricingRegionBasedService dynamicPricingRegionBasedService;
  @MockBean private RegionModelDistributionService regionModelDistributionService;

  @Test
  void shouldGetRequestFareCountSuccess() throws Exception {
    RegionModelDistributionEntity distributionEntity = new RegionModelDistributionEntity();
    distributionEntity.setRegionId(1L);
    distributionEntity.setModels(
        List.of(new ModelPercentage(1L, BigDecimal.valueOf(100L), "v4_test")));
    Optional<RegionModelDistributionEntity> distributionEntityOptional =
        Optional.of(distributionEntity);

    when(dynamicPricingService.getMultiFare(any())).thenReturn(new MultiFareResponse());
    when(dynamicPricingRegionBasedService.getMultiFare(any())).thenReturn(new MultiFareResponse());
    when(regionModelDistributionService.getEffectiveRegionModelDistribution(any()))
        .thenReturn(distributionEntityOptional);

    GetEstimatedFareInboundRequest request = new GetEstimatedFareInboundRequest();
    request.setCountryCode("65");
    request.setBookingChannel(GetEstimatedFareInboundRequest.BookingChannelEnum.IPHONE);
    request.setMobile("91234567");
    request.setJobType(GetEstimatedFareInboundRequest.JobTypeEnum.IMMEDIATE);
    request.setPickupAddressRef("154505");
    request.setPickupAddressLat(1.2345);
    request.setPickupAddressLng(103.1234);
    request.setPickupZoneId("95");
    request.setDestAddressRef("1001");
    request.setDestAddressLat(1.3579843);
    request.setDestAddressLng(103.7890947);
    request.setDestZoneId("95");
    request.setVehTypeIDs(List.of(1));
    request.setFareDate(OffsetDateTime.now());

    // request with region 1
    dynamicPricingServiceApi.getMultiFare(request).execute();
    dynamicPricingServiceApi.getMultiFare(request).execute();
    dynamicPricingServiceApi.getMultiFare(request).execute();
    dynamicPricingServiceApi.getMultiFare(request).execute();
    dynamicPricingServiceApi.getMultiFare(request).execute();

    // request with region 2
    request.setPickupAddressLat(2.3456);
    request.setPickupAddressLng(104.2345);
    dynamicPricingServiceApi.getMultiFare(request).execute();
    dynamicPricingServiceApi.getMultiFare(request).execute();

    // Hear manually trigger sync to Redis, instead of scheduled task.
    requestCounterService.syncLocalDataToRedis();

    Instant now = Instant.now();
    Map<Long, Long> regionRequestCountMap =
        requestCounterService.getRequestCountByRegions(
            RequestCountConstant.MULTI_FARE, now.minus(Duration.ofDays(1)), now);

    assertEquals(5, regionRequestCountMap.get(1L));
    assertEquals(2, regionRequestCountMap.get(2L));
  }
}
