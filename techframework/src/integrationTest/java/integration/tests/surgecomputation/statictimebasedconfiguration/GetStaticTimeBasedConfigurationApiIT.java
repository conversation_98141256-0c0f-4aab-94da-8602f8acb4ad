package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationCreateResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequestAppliedHoursInner;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetStaticTimeBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private Long configurationId;

  @BeforeEach
  public void setupTest() throws IOException {
    // Create a configuration to retrieve in the tests
    StaticTimeBasedConfigurationRequest configRequest = new StaticTimeBasedConfigurationRequest();
    configRequest.setName("Test Config for Retrieval");
    configRequest.setVersion("1.0");
    configRequest.setEffectiveFrom(OffsetDateTime.now());
    configRequest.setDescription("Configuration for testing retrieval");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    configRequest.setAppliedHours(appliedHours);

    Response<StaticTimeBasedConfigurationCreateResponse> configResponse =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(configRequest), TEST_USER_ID)
            .execute();

    assertTrue(configResponse.isSuccessful());
    assertNotNull(configResponse.body());
    configurationId = configResponse.body().getData().get(0).getId();
  }

  @Test
  @DisplayName("Should get a static time-based configuration by ID successfully")
  public void testGetStaticTimeBasedConfigurationById_Success() throws IOException {
    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurationById(configurationId).execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticTimeBasedConfigurationResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(configurationId, responseBody.getData().getId());
    assertEquals("Test Config for Retrieval", responseBody.getData().getName());
    assertEquals("1.0", responseBody.getData().getVersion());
    assertNotNull(responseBody.getData().getEffectiveFrom());
    assertEquals("Configuration for testing retrieval", responseBody.getData().getDescription());

    // Verify applied hours
    assertNotNull(responseBody.getData().getAppliedHours());
    assertEquals(1, responseBody.getData().getAppliedHours().size());
    assertEquals("MON", responseBody.getData().getAppliedHours().get(0).getDayOfWeek().toString());
    assertEquals(8, responseBody.getData().getAppliedHours().get(0).getHourOfDay());
    assertEquals("1.5", responseBody.getData().getAppliedHours().get(0).getValue());

    // Verify audit fields
    assertEquals(TEST_USER_ID, responseBody.getData().getCreatedBy());
    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getCreatedDate());
    assertNotNull(responseBody.getData().getUpdatedDate());
    assertNotNull(responseBody.getTimestamp());
    assertNotNull(responseBody.getTraceId());
  }

  @Test
  @DisplayName("Should return 404 Not Found when getting a non-existent configuration")
  public void testGetStaticTimeBasedConfigurationById_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 999999L;

    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurationById(nonExistentId).execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }
}
