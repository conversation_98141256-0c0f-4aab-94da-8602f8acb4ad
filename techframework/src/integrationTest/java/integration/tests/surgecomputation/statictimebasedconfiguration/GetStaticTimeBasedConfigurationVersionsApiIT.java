package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticBasedConfigurationVersionListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationCreateResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequestAppliedHoursInner;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class GetStaticTimeBasedConfigurationVersionsApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";

  @Test
  public void testGetStaticTimeBasedConfigurationVersions_Success() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime effectiveFrom = now.minusDays(20);
    OffsetDateTime effectiveTo = now.minusDays(10);
    OffsetDateTime secondEffectiveFrom = now.minusDays(1);

    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest secondRequest = new StaticTimeBasedConfigurationRequest();
    secondRequest.setName("Test second Config");
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(secondEffectiveFrom);
    secondRequest.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> secondAppliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "5.5"));
    secondRequest.setAppliedHours(secondAppliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();
    Response<StaticTimeBasedConfigurationCreateResponse> response2 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(secondRequest), TEST_USER_ID)
            .execute();

    assertTrue(response.isSuccessful());
    assertTrue(response2.isSuccessful());

    // Get all versions
    Response<StaticBasedConfigurationVersionListResponse> versionResponse =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurationVersions().execute();

    assertTrue(versionResponse.isSuccessful());
    assertEquals(200, versionResponse.code());

    StaticBasedConfigurationVersionListResponse versionResponseBody = versionResponse.body();
    assertNotNull(versionResponseBody);
    assertNotNull(versionResponseBody.getData());
    assertEquals(2, versionResponseBody.getData().size());
    assertEquals("2.0", versionResponseBody.getData().get(0).getVersion());
    assertEquals(true, versionResponseBody.getData().get(0).getIsInUse());
  }

  @Test
  public void testGetStaticTimeBasedConfigurationVersions_Success_withNoEffectiveVersion()
      throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime effectiveFrom = now.minusDays(20);
    OffsetDateTime effectiveTo = now.minusDays(10);
    OffsetDateTime secondEffectiveFrom = now.plusDays(1);

    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(effectiveFrom);
    request.setEffectiveTo(effectiveTo);
    request.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest secondRequest = new StaticTimeBasedConfigurationRequest();
    secondRequest.setName("Test second Config");
    secondRequest.setVersion("2.0");
    secondRequest.setEffectiveFrom(secondEffectiveFrom);
    secondRequest.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> secondAppliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "5.5"));
    secondRequest.setAppliedHours(secondAppliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();
    Response<StaticTimeBasedConfigurationCreateResponse> response2 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(secondRequest), TEST_USER_ID)
            .execute();

    assertTrue(response.isSuccessful());
    assertTrue(response2.isSuccessful());

    // Get all versions
    Response<StaticBasedConfigurationVersionListResponse> versionResponse =
        dynamicPricingServiceApi.getStaticTimeBasedConfigurationVersions().execute();

    assertTrue(versionResponse.isSuccessful());
    assertEquals(200, versionResponse.code());

    StaticBasedConfigurationVersionListResponse versionResponseBody = versionResponse.body();
    assertNotNull(versionResponseBody);
    assertNotNull(versionResponseBody.getData());
    assertEquals(2, versionResponseBody.getData().size());
    assertEquals("2.0", versionResponseBody.getData().get(0).getVersion());
    assertEquals(false, versionResponseBody.getData().get(0).getIsInUse());
    assertEquals(true, versionResponseBody.getData().get(1).getIsInUse());
  }
}
