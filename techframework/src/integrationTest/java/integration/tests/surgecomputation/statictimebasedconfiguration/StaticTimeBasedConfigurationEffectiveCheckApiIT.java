package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticBasedConfigurationEffectiveCheckResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationCreateResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequestAppliedHoursInner;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class StaticTimeBasedConfigurationEffectiveCheckApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private static final String VERSION = "1.0";

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNull() throws IOException {
    createStaticTimeBasedConfiguration(OffsetDateTime.now(), null);

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticTimeBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenHasEffectiveConfig_withEffectiveIsNotNull() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticTimeBasedConfiguration(now, now.plusDays(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticTimeBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertFalse(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenNoEffectiveConfig_withOutOfDate() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticTimeBasedConfiguration(now.minusDays(1), now.minusSeconds(10));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticTimeBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenNoEffectiveConfig_withCloseToExpiration() throws IOException {
    OffsetDateTime now = OffsetDateTime.now();
    createStaticTimeBasedConfiguration(now.minusDays(1), now.plusDays(1));

    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticTimeBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertTrue(checkResponse.body().getIsEffective());
    assertEquals(VERSION, checkResponse.body().getVersion());
    assertNotNull(checkResponse.body().getEffectiveFrom());
    assertNotNull(checkResponse.body().getEffectiveTo());
  }

  @Test
  public void shouldNotWarning_whenNoEffectiveConfig_withNoAnyConfig() throws IOException {
    Response<StaticBasedConfigurationEffectiveCheckResponse> checkResponse =
        dynamicPricingServiceApi.staticTimeBasedConfigurationEffectiveCheck().execute();

    assertTrue(checkResponse.isSuccessful());
    assertNotNull(checkResponse.body());
    assertTrue(checkResponse.body().getIsWarning());
    assertFalse(checkResponse.body().getIsEffective());
    assertNull(checkResponse.body().getVersion());
    assertNull(checkResponse.body().getEffectiveFrom());
    assertNull(checkResponse.body().getEffectiveTo());
  }

  private void createStaticTimeBasedConfiguration(
      final OffsetDateTime effectiveFrom, final OffsetDateTime effectiveTo) throws IOException {
    StaticTimeBasedConfigurationRequest createRequest = new StaticTimeBasedConfigurationRequest();
    createRequest.setName("Time Config");
    createRequest.setVersion(VERSION);
    createRequest.setEffectiveFrom(effectiveFrom);
    createRequest.setEffectiveTo(effectiveTo);
    createRequest.setDescription("Time Config");

    // Create applied hours for first configuration
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "6.2"));
    createRequest.setAppliedHours(appliedHours);

    Response<StaticTimeBasedConfigurationCreateResponse> createResponse =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(createRequest), TEST_USER_ID)
            .execute();

    assertTrue(createResponse.isSuccessful());
  }
}
