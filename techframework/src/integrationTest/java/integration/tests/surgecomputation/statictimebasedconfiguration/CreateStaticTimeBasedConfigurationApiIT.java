package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.StaticTimeBasedConfigurationJPARepository;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CreateStaticTimeBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";

  private final StaticTimeBasedConfigurationJPARepository staticConfigRepository;

  @Test
  @DisplayName("Should create a static time-based configuration successfully")
  public void testCreateStaticTimeBasedConfiguration_Success() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());
    assertNotNull(response.body());

    StaticTimeBasedConfiguration configuration = response.body().getData().get(0);
    assertNotNull(configuration.getId());
    assertEquals("Test Config", configuration.getName());
    assertEquals("1.0", configuration.getVersion());
    assertNotNull(configuration.getEffectiveFrom());
    assertEquals("Test configuration for peak hour surge pricing", configuration.getDescription());

    // Verify applied hours
    assertNotNull(configuration.getAppliedHours());
    assertEquals(1, configuration.getAppliedHours().size());
    assertEquals(
        StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON,
        configuration.getAppliedHours().get(0).getDayOfWeek());
    assertEquals(Integer.valueOf(8), configuration.getAppliedHours().get(0).getHourOfDay());
    assertEquals("1.5", configuration.getAppliedHours().get(0).getValue());

    assertEquals(TEST_USER_ID, configuration.getCreatedBy());
    assertEquals(TEST_USER_ID, configuration.getUpdatedBy());
    assertNotNull(configuration.getCreatedDate());
    assertNotNull(configuration.getUpdatedDate());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when name is missing")
  public void testCreateStaticTimeBasedConfiguration_MissingName() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when version is missing")
  public void testCreateStaticTimeBasedConfiguration_MissingVersion() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.THU, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when effective_from is missing")
  public void testCreateStaticTimeBasedConfiguration_MissingEffectiveFrom() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setDescription("Test configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when applied hours is missing")
  public void testCreateStaticTimeBasedConfiguration_MissingAppliedHours() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testCreateStaticTimeBasedConfiguration_MissingUserIdHeader() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), null)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when applied hour has invalid hour of day")
  public void testCreateStaticTimeBasedConfiguration_InvalidHourOfDay() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Test configuration");

    // Create applied hours with invalid hour of day
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON,
                24, // Invalid: should be 0-23
                "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should reject configuration with overlapping effective date ranges")
  public void testCreateStaticTimeBasedConfiguration_OverlappingDateRanges() throws IOException {
    // Arrange - Create first configuration
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Overlap Test Config");
    request.setVersion("1.0");
    request.setEffectiveFrom(now);
    request.setEffectiveTo(later);
    request.setDescription("First configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationCreateResponse> response1 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request), TEST_USER_ID)
            .execute();

    // Assert - First configuration should be created successfully
    assertTrue(response1.isSuccessful());
    assertEquals(200, response1.code());

    // Arrange - Create second configuration with overlapping date range
    StaticTimeBasedConfigurationRequest request2 = new StaticTimeBasedConfigurationRequest();
    request2.setName("Overlap Test Config"); // Same name
    request2.setVersion("2.0"); // Different version
    request2.setEffectiveFrom(now.plusDays(15)); // Overlaps with first configuration
    request2.setEffectiveTo(later.plusDays(30));
    request2.setDescription("Second configuration with overlapping date range");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "3.5"));
    request2.setAppliedHours(appliedHours2);

    // Act - Try to create second configuration with overlapping date range
    Response<StaticTimeBasedConfigurationCreateResponse> response2 =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request2), TEST_USER_ID)
            .execute();

    // Assert - Second configuration should be rejected
    assertFalse(response2.isSuccessful());
    assertEquals(400, response2.code()); // Expecting a 400 Bad Request due to constraint violation
  }

  @Test
  @DisplayName("Should return 400 Bad Request with batch create")
  public void testCreateStaticTimeBasedConfiguration_DifferentVersion_Batch() throws IOException {
    // Arrange - Create first configuration
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);

    StaticTimeBasedConfigurationRequest request1 = new StaticTimeBasedConfigurationRequest();
    request1.setName("Test Config");
    request1.setVersion("1.0");
    request1.setEffectiveFrom(now);
    request1.setEffectiveTo(later);
    request1.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request1.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest request2 = new StaticTimeBasedConfigurationRequest();
    request2.setName("Test Config 2");
    request2.setVersion("2.0"); // Different version
    request2.setEffectiveFrom(now);
    request2.setEffectiveTo(later);
    request2.setDescription("Test configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request2.setAppliedHours(appliedHours2);

    // Act - Try to create second configuration with different version
    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request1, request2), TEST_USER_ID)
            .execute();

    // Assert - Second configuration should be rejected
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code()); // Expecting a 400 Bad Request due to constraint violation
  }

  @Test
  public void shouldSuccessWithOverlappingPeriods_whenPreviousVersionEffectiveToIsNull()
      throws IOException {
    // Arrange - Create first configuration
    String name1 = "Overlap Test Config1";
    String name2 = "Overlap Test Config2";

    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime overlappingFrom = now.plusDays(10);

    create2ConfigurationsWithEffectiveToIsNull(name1, name2, now, "1.0");

    // Arrange - Create configurations with overlapping date range
    create2ConfigurationsWithEffectiveToIsNull(name1, name2, overlappingFrom, "2.0");

    List<StaticTimeBasedConfigurationJPA> configurations =
        staticConfigRepository.findAllByVersion("1.0");

    assertEquals(2, configurations.size());

    for (final StaticTimeBasedConfigurationJPA configuration : configurations) {
      assertNotNull(configuration.getEffectiveTo());
      OffsetDateTime expected = overlappingFrom.minusSeconds(1);
      OffsetDateTime actual =
          configuration.getEffectiveTo().atZone(ZoneOffset.systemDefault()).toOffsetDateTime();

      // Handle timestamp precision differences between Java and PostgreSQL
      // Use a tolerance-based comparison instead of exact equality
      long expectedEpochMicros =
          expected.toInstant().toEpochMilli() * 1000 + expected.toInstant().getNano() / 1000;
      long actualEpochMicros =
          actual.toInstant().toEpochMilli() * 1000 + actual.toInstant().getNano() / 1000;

      assertTrue(
          Math.abs(expectedEpochMicros - actualEpochMicros) <= 1,
          String.format(
              "Timestamp difference too large. Expected: %s, Actual: %s", expected, actual));
    }
  }

  private void create2ConfigurationsWithEffectiveToIsNull(
      final String name1, final String name2, final OffsetDateTime now, final String version)
      throws IOException {
    StaticTimeBasedConfigurationRequest request1 = new StaticTimeBasedConfigurationRequest();
    request1.setName(name1);
    request1.setVersion(version);
    request1.setEffectiveFrom(now);
    request1.setDescription("First configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    request1.setAppliedHours(appliedHours);

    StaticTimeBasedConfigurationRequest request2 = new StaticTimeBasedConfigurationRequest();
    request2.setName(name2);
    request2.setVersion(version);
    request2.setEffectiveFrom(now);
    request2.setDescription("Second configuration");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "2.5"));
    request2.setAppliedHours(appliedHours2);

    Response<StaticTimeBasedConfigurationCreateResponse> response =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(request1, request2), TEST_USER_ID)
            .execute();

    assertTrue(response.isSuccessful());
  }
}
