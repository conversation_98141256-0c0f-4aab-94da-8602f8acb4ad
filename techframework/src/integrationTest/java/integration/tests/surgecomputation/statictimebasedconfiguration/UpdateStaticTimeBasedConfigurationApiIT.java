package integration.tests.surgecomputation.statictimebasedconfiguration;

import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationCreateResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationRequestAppliedHoursInner;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.StaticTimeBasedConfigurationResponse;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class UpdateStaticTimeBasedConfigurationApiIT extends IntegrationTestBase {

  private static final String TEST_USER_ID = "test-user";
  private Long configurationId;

  @BeforeEach
  public void setupTest() throws IOException {
    // Create a configuration to update in the tests
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);

    StaticTimeBasedConfigurationRequest configRequest = new StaticTimeBasedConfigurationRequest();
    configRequest.setName("Initial Config");
    configRequest.setVersion("1.0");
    configRequest.setEffectiveFrom(now);
    configRequest.setEffectiveTo(later);
    configRequest.setDescription("Initial configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    configRequest.setAppliedHours(appliedHours);

    Response<StaticTimeBasedConfigurationCreateResponse> configResponse =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(configRequest), TEST_USER_ID)
            .execute();

    if (!configResponse.isSuccessful()) {
      fail("Failed to create configuration: " + configResponse.code());
    }

    assertNotNull(configResponse.body());
    configurationId = configResponse.body().getData().get(0).getId();
  }

  @Test
  @DisplayName("Should update a static time-based configuration successfully")
  public void testUpdateStaticTimeBasedConfiguration_Success() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Updated Config");
    request.setVersion("1.1");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Updated configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.TUE, 9, "2.0"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticTimeBasedConfiguration(configurationId, request, TEST_USER_ID)
            .execute();

    // Assert
    assertTrue(response.isSuccessful());
    assertEquals(200, response.code());

    StaticTimeBasedConfigurationResponse responseBody = response.body();
    assertNotNull(responseBody);
    assertNotNull(responseBody.getData());
    assertEquals(configurationId, responseBody.getData().getId());
    assertEquals("Updated Config", responseBody.getData().getName());
    assertEquals("1.1", responseBody.getData().getVersion());
    assertNotNull(responseBody.getData().getEffectiveFrom());
    assertEquals(
        "Updated configuration for peak hour surge pricing",
        responseBody.getData().getDescription());

    // Verify applied hours
    assertNotNull(responseBody.getData().getAppliedHours());
    assertEquals(1, responseBody.getData().getAppliedHours().size());
    assertEquals(
        StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.TUE.toString(),
        responseBody.getData().getAppliedHours().get(0).getDayOfWeek().toString());
    assertEquals(
        Integer.valueOf(9), responseBody.getData().getAppliedHours().get(0).getHourOfDay());
    assertEquals("2.0", responseBody.getData().getAppliedHours().get(0).getValue());

    assertEquals(TEST_USER_ID, responseBody.getData().getUpdatedBy());
    assertNotNull(responseBody.getData().getUpdatedDate());
  }

  @Test
  @DisplayName("Should return 404 Not Found when updating a non-existent configuration")
  public void testUpdateStaticTimeBasedConfiguration_NotFound() throws IOException {
    // Arrange
    Long nonExistentId = 999999L;
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Updated Config");
    request.setVersion("1.1");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Updated configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.TUE, 9, "2.0"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticTimeBasedConfiguration(nonExistentId, request, TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(404, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when X-User-Id header is missing")
  public void testUpdateStaticTimeBasedConfiguration_MissingUserIdHeader() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    request.setName("Updated Config");
    request.setVersion("1.1");
    request.setEffectiveFrom(OffsetDateTime.now());
    request.setDescription("Updated configuration for peak hour surge pricing");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.TUE, 9, "2.0"));
    request.setAppliedHours(appliedHours);

    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticTimeBasedConfiguration(configurationId, request, null)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should return 400 Bad Request when required fields are missing")
  public void testUpdateStaticTimeBasedConfiguration_MissingRequiredFields() throws IOException {
    // Arrange
    StaticTimeBasedConfigurationRequest request = new StaticTimeBasedConfigurationRequest();
    // Missing name, version, effectiveFrom, appliedHours

    // Act
    Response<StaticTimeBasedConfigurationResponse> response =
        dynamicPricingServiceApi
            .updateStaticTimeBasedConfiguration(configurationId, request, TEST_USER_ID)
            .execute();

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  @DisplayName("Should reject update with overlapping effective date ranges")
  public void testUpdateStaticTimeBasedConfiguration_OverlappingDateRanges() throws IOException {
    // Arrange - Create a second configuration with non-overlapping date range
    OffsetDateTime now = OffsetDateTime.now();
    OffsetDateTime later = now.plusDays(30);
    OffsetDateTime evenLater = later.plusDays(1); // One day after the first config ends
    OffsetDateTime furtherLater = evenLater.plusDays(30);

    // First configuration is already created in setupTestData() with current time

    // Create a second configuration with non-overlapping date range
    StaticTimeBasedConfigurationRequest configRequest = new StaticTimeBasedConfigurationRequest();
    configRequest.setName("Initial Config"); // Same name as first config
    configRequest.setVersion("2.0"); // Different version
    configRequest.setEffectiveFrom(evenLater); // Starts after first config ends
    configRequest.setEffectiveTo(furtherLater);
    configRequest.setDescription("Second configuration with non-overlapping date range");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "1.5"));
    configRequest.setAppliedHours(appliedHours);

    Response<StaticTimeBasedConfigurationCreateResponse> configResponse =
        dynamicPricingServiceApi
            .batchCreateStaticTimeBasedConfiguration(List.of(configRequest), TEST_USER_ID)
            .execute();

    assertTrue(configResponse.isSuccessful());
    assertNotNull(configResponse.body());
    Long secondConfigId = configResponse.body().getData().get(0).getId();

    // Now try to update the second configuration to overlap with the first one
    StaticTimeBasedConfigurationRequest updateRequest = new StaticTimeBasedConfigurationRequest();
    updateRequest.setName("Initial Config"); // Same name
    updateRequest.setVersion("2.0"); // Same version as second config
    updateRequest.setEffectiveFrom(now.plusDays(15)); // Now overlaps with first config
    updateRequest.setEffectiveTo(furtherLater);
    updateRequest.setDescription("Updated second configuration to overlap with first");

    // Create applied hours
    List<StaticTimeBasedConfigurationRequestAppliedHoursInner> appliedHours2 =
        List.of(
            new StaticTimeBasedConfigurationRequestAppliedHoursInner(
                StaticTimeBasedConfigurationRequestAppliedHoursInner.DayOfWeekEnum.MON, 8, "5.5"));
    updateRequest.setAppliedHours(appliedHours2);

    // Act - Try to update the second configuration to overlap with the first
    Response<StaticTimeBasedConfigurationResponse> updateResponse =
        dynamicPricingServiceApi
            .updateStaticTimeBasedConfiguration(secondConfigId, updateRequest, TEST_USER_ID)
            .execute();

    // Assert - Update should be rejected
    assertFalse(updateResponse.isSuccessful());
    assertEquals(
        400, updateResponse.code()); // Expecting a 400 Bad Request due to constraint violation
  }
}
