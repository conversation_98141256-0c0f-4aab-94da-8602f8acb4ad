spring.application.name=ngp-me-dynamicpricing-svc

# RELEASE VERSION
dps.system.param.applicationRelease=1

# Log Format
logging.pattern.level=[${spring.application.name:},%X{traceId:-},%X{spanId:-}] %5p

# Liquibase config
spring.liquibase.change-log=classpath:db/db.changelog-master.xml
spring.liquibase.enabled=false

# Database config
# Legacy configuration (kept for backward compatibility)
spring.datasource.url=**************************************************************************
spring.datasource.username=user
spring.datasource.password=
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Writer database configuration
spring.datasource.writer.url=**************************************************************************
spring.datasource.writer.username=user
spring.datasource.writer.password=
spring.datasource.writer.driver-class-name=org.postgresql.Driver

# Reader database configuration
spring.datasource.reader.url=**************************************************************************
spring.datasource.reader.username=user
spring.datasource.reader.password=
spring.datasource.reader.driver-class-name=org.postgresql.Driver

# Redis config
me.redis.defaultExpiredTimeInSecond=3600
spring.data.redis.cluster.nodes=
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.username=default
spring.data.redis.password=admin

#CMS Config
spring.cloud.config.enabled=true
spring.cloud.config.name=me-pricing

# Spring Cloud Bus Stream Kafka
spring.cloud.bus.ack.enabled=false
spring.cloud.bus.destination=ngp.common.cms.refresh_setting
spring.cloud.stream.kafka.binder.consumer-properties.auto.offset.reset=latest

# Exclude OpenTelemetryAutoConfiguration from Spring Boot Actuator as we use the Agent
spring.autoconfigure.exclude=org.springframework.boot.actuate.autoconfigure.tracing.OpenTelemetryAutoConfiguration

# Actuator Config
management.endpoints.web.exposure.include=*
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.minimum-expected-value.http.server.requests=5ms
management.metrics.distribution.maximum-expected-value.http.server.requests=30000ms
management.endpoint.health.show-details=always
management.endpoint.loggers.enabled=true
management.info.git.mode=full

# Config logbook
logging.level.org.zalando.logbook=trace

# OpenAPI Config
springdoc.api-docs.enabled=true
springdoc.api-docs.resolve-schema-properties=true
springdoc.writer-with-default-pretty-printer=true
# springdoc.pre-loading-enabled=true
springdoc.default-flat-paramObject=true
# springdoc.default-support-form-data=true
springdoc.swagger-ui.defaultModelRendering=example
springdoc.swagger-ui.showCommonExtensions=true

spring.jpa.open-in-view=false

# AWS Parameter Store integration
spring.cloud.aws.parameterstore.region=ap-southeast-1

# Feign Config
spring.cloud.openfeign.circuitbreaker.enabled=true
spring.cloud.openfeign.client.config.default.connectTimeout=5000
spring.cloud.openfeign.client.config.default.readTimeout=5000
spring.cloud.openfeign.client.config.default.loggerLevel=basic

# Feign Client
openfeign.addressClient.name=addressClient
spring.cloud.openfeign.client.config.addressClient.url=https://mob-me-address-app.apps.sg.sit.zig.systems

openfeign.fareClient.name=fareClient
spring.cloud.openfeign.client.config.fareClient.url=https://mob-me-fare-app.apps.sg.sit.zig.systems

openfeign.fleetAnalyticClient.name=fleetAnalyticClient
spring.cloud.openfeign.client.config.fleetAnalyticClient.url=https://mob-me-fleetanalytic-app.apps.sg.sit.zig.systems

openfeign.cmsClient.name=cmsClient
spring.cloud.openfeign.client.config.cmsClient.url=https://mob-common-cms-app.apps.sg.sit.zig.systems

openfeign.weatherRetrievalClient.name=weatherRetrievalClient
spring.cloud.openfeign.client.config.weatherRetrievalClient.url=https://mob-me-weather-retrieval-app.apps.sg.sit.zig.systems

# No real use openfeign for this one
spring.cloud.openfeign.client.config.surgeComputationModelClient.url=https://me-surge-computation-model-sit.internal.eks.sg.nprod.zig.systems

# Dynamic surge
newPricingModelConfig.items=
dosExcludeBookingChannels=GOJEK
dosIncludeJobStatuses=FAILED,CANCELLED,CANCELLED_UR,NTA
fleetAnalyticDemand15={"demand_15": {"from": 2,"to": 12, "job_types": ["IMMEDIATE"]},"demand_15_pre": { "from": 7,"to": 17, "job_types": ["IMMEDIATE"]}}
std001Weight=0.7
fleetAnalyticDemand30=30
fleetAnalyticDemand60=60
new-pricing-model-config.items=
# The get fare count sampling period in minutes. e.g.: "10,0", means between (now - 10) and (now - 0); "60,30", means between (now - 60) and (now - 30).
dynamicPricingSurgeSamplingPeriodInMinutes=5,2
# The global rate config how many percent request will use region fare, min 0, max 100. e.g.: 80 means the random num in (0,80] will use region based, (80,100] will use zone based.
globalRateToRegionForGetFare=80

#ConfigManagementService
bookRideSchedulingMin=2
feedId=comfortdelgro
currencyCode=SGD
bookRideVehicleGroup=0
bookRideProduct=COMFORT,METERED
comfortLocalisedName=ComfortRIDE
meteredLocalisedName=Metered Fare
comfortInternalName=FLAT001
meteredInternalName=STD001
waitingTimeSeconds=1
fileName=comfortdelgro
comfortLowRangeEstimateMultiplierValue=0.9
meteredLowRangeEstimateMultiplierValue=1
comfortHighRangeEstimateMultiplierValue=1
meteredHighRangeEstimateMultiplierValue=1.15
minimumFixed=4.2
bookRideProdCategory=5
carIconId=comfortdelgro-taxi

#SftpConfig
SFTP_ENDPOINT =
SFTP_PRIVATE_KEY =
SFTP_USER =
SFTP_DIRECTORY =

# Dynamic surge
dps.system.param.dynamicSurge.forceUsingR1.enabled=false

# ===============================================================================
# Partition Management Configuration for surge_computation_model_api_logs Table
# ===============================================================================
# This configuration manages automatic partition creation/cleanup for the 
# surge_computation_model_api_logs table, which is partitioned by Singapore timezone months.
# The table stores API call logs for surge computation models and requires automatic
# partition management to handle high-volume data efficiently.

# Number of months ahead to create partitions during application startup
# Creates partitions for current month + this many future months on app start
# Default: 3 (creates partitions for current + next 3 months)
app.partition.startup-months-ahead=3

# Number of months ahead to create partitions during scheduled maintenance
# The daily scheduled task will ensure partitions exist for this many future months
# Default: 6 (ensures 6 months of future partitions always exist)
app.partition.scheduled-months-ahead=6

# Number of months of historical data to retain before automatic cleanup
# Partitions older than this will be automatically dropped to save storage space
# Default: 36 (keeps 3 years of historical API log data for audit and compliance)
app.partition.months-to-keep=36

# Enable/disable automatic cleanup of old partitions
# When true, partitions older than 'months-to-keep' will be automatically dropped
# ⚠️ WARNING: This permanently deletes old data! Ensure proper backup/archival before enabling
# Default: false (automatic cleanup disabled for safety - manual cleanup recommended)
app.partition.enable-cleanup=false

# Enable/disable scheduled partition maintenance tasks
# When true, daily maintenance will create future partitions and cleanup old ones
# Default: true (scheduled maintenance enabled)
app.partition.enable-scheduled-maintenance=true

# Cron expression for partition maintenance schedule
# Options:
# - Daily: "0 0 1 * * ?" (runs daily at 1:00 AM Singapore time) - Higher fault tolerance
# - Monthly: "0 0 1 1 * ?" (runs monthly on 1st at 1:00 AM) - More logical, less overhead
# Default: "0 0 1 * * ?" (daily for maximum reliability)
# Format: second minute hour day month weekday
app.partition.maintenance-cron=0 0 1 * * ?

# Timezone for partition maintenance schedule
# All partition operations are aligned to Singapore business timezone
# Default: Asia/Singapore (Singapore Standard Time - UTC+8)
app.partition.maintenance-timezone=Asia/Singapore