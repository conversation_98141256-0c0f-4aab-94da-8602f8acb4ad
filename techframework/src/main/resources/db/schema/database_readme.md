# Database Schema Documentation

## Table: `surge_computation_model_api_logs`

### Overview

The `surge_computation_model_api_logs` table is designed to store comprehensive API call logs for surge computation models. This table serves as a critical component for monitoring, debugging, and analyzing the performance of external surge computation model services.

### Purpose and Use Cases

- **API Monitoring**: Track all API calls to surge computation models
- **Performance Analysis**: Monitor response times and success rates
- **Debugging**: Store request/response data for troubleshooting
- **Audit Trail**: Maintain complete records of model interactions
- **Business Intelligence**: Analyze model usage patterns and effectiveness

### Table Structure

```sql
CREATE TABLE surge_computation_model_api_logs (
    id             BIGINT NOT NULL DEFAULT nextval('surge_computation_model_api_logs_id_seq'),
    model_id       BIGINT       NOT NULL,
    model_name     VARCHAR(255) NOT NULL,
    endpoint_url   VARCHAR(255) NOT NULL,
    create_timestamp TIMESTAMP    NOT NULL DEFAULT (now() at time zone 'utc')::timestamp,
    status_code    INT          NOT NULL,
    request_params JSONB        NOT NULL,
    response_body  JSONB        NOT NULL,
    singapore_month DATE NOT NULL DEFAULT (get_singapore_month((now() at time zone 'utc')::timestamp)),
    PRIMARY KEY (id, singapore_month)
) PARTITION BY RANGE (singapore_month);
```

#### Column Descriptions

| Column | Type | Description |
|--------|------|-------------|
| `id` | BIGINT | Primary key for the API log entry (auto-increment) |
| `model_id` | BIGINT | Reference to the surge computation model |
| `model_name` | VARCHAR(255) | Name of the surge computation model |
| `endpoint_url` | VARCHAR(255) | The API endpoint that was called |
| `create_timestamp` | TIMESTAMP | UTC timestamp when the API call was made |
| `status_code` | INT | HTTP status code of the response |
| `request_params` | JSONB | Dynamic request parameters in JSON format |
| `response_body` | JSONB | Dynamic response data in JSON format |
| `singapore_month` | DATE | Auto-generated Singapore timezone month for partitioning |

### Partitioning Strategy

#### Design Principles

The table implements **monthly partitioning based on Singapore timezone** to optimize performance and manage data growth effectively.

#### Key Features

1. **Singapore Timezone Alignment**: Partitions are created based on Singapore business calendar
2. **Monthly Granularity**: Balances performance with manageable partition count
3. **Automatic Partition Management**: No manual intervention required
4. **Intelligent Data Retention**: Automatic cleanup of old partitions
5. **UTC Storage**: Timestamps stored in UTC for consistency, converted to Singapore timezone for partitioning

#### Partition Naming Convention

```
surge_computation_model_api_logs_YYYY_MM
```

Examples:
- `surge_computation_model_api_logs_2024_01` (January 2024)
- `surge_computation_model_api_logs_2024_02` (February 2024)

### Automated Partition Management

#### Components

1. **Database Functions** (`02_standard_convert_to_partitioned_table.sql`)
   - `get_singapore_month(ts TIMESTAMP)`: Converts UTC timestamp to Singapore timezone month
   - `create_monthly_partition_for_logs(target_month DATE, table_name TEXT)`: Creates partition for specific month
   - `ensure_monthly_partitions_exist(months_ahead INTEGER)`: Ensures required partitions exist
   - `cleanup_old_log_partitions(months_to_keep INTEGER)`: Removes old partitions for data retention
   - **Sequence Management**: Automatically creates and manages `surge_computation_model_api_logs_id_seq`

2. **Application Service** (`PartitionManagementService`)
   - Startup partition creation
   - Scheduled maintenance tasks
   - Manual partition management
   - Monitoring and reporting

3. **Configuration Management** (`PartitionConfiguration`)
   - Flexible parameter configuration
   - Environment-specific settings

4. **Check Constraint**: Ensures data integrity with constraint `chk_singapore_month_matches_timestamp`
   ```sql
   CHECK (singapore_month = get_singapore_month(create_timestamp))
   ```

5. **Automatic Partition Creation**: Initial partitions created for current month + 6 months ahead

#### Automation Workflow

##### Application Startup
- Automatically creates partitions for current month + next 3 months
- Non-blocking operation (failures are logged but don't prevent startup)

##### Daily Maintenance (1 AM Singapore Time)
- Creates partitions for next 6 months
- Cleans up partitions older than 24 months
- Comprehensive logging of all operations

##### Configuration Options

```properties
# Partition Management Configuration
app.partition.startup-months-ahead=3          # Partitions created on startup
app.partition.scheduled-months-ahead=6        # Partitions created by scheduled task
app.partition.months-to-keep=24               # Months of data retention
app.partition.enable-cleanup=true             # Enable automatic cleanup
app.partition.enable-scheduled-maintenance=true # Enable scheduled tasks
app.partition.maintenance-cron=0 0 1 * * ?    # Daily at 1 AM SGT
app.partition.maintenance-timezone=Asia/Singapore
```

#### Function Details

##### `get_singapore_month(ts TIMESTAMP)`
- **Purpose**: Convert UTC timestamp to Singapore timezone month
- **Returns**: DATE (first day of the month in Singapore timezone)
- **Example**: `get_singapore_month('2024-06-15 16:30:00'::timestamp)` returns `2024-06-01`

##### `create_monthly_partition_for_logs(target_month DATE, table_name TEXT DEFAULT 'surge_computation_model_api_logs')`
- **Purpose**: Create a new monthly partition with indexes
- **Creates**: Partition table with proper constraints and indexes
- **Indexes Created**:
  - `idx_[partition_name]_model_timestamp` on `(model_name, create_timestamp DESC)`
  - `idx_[partition_name]_status` on `(status_code)`
  - `idx_[partition_name]_model_id` on `(model_id)`

##### `ensure_monthly_partitions_exist(months_ahead INTEGER DEFAULT 3)`  
- **Purpose**: Ensure required partitions exist for current and future months
- **Returns**: Array of created partition names
- **Usage**: Called during application startup and maintenance

##### `cleanup_old_log_partitions(months_to_keep INTEGER DEFAULT 24)`
- **Purpose**: Remove old partitions to manage storage
- **Returns**: Array of dropped partition names
- **Safety**: Only drops partitions older than specified retention period

#### Migration Strategy

The table conversion from regular to partitioned is handled by Liquibase changeset `02_standard_convert_to_partitioned_table.sql`:

1. **Step 1-3**: Create partition management functions
2. **Step 4**: Backup existing table (`surge_computation_model_api_logs_backup`)
3. **Step 5**: Create new partitioned table structure with sequence
4. **Step 6**: Add comprehensive table and column comments
5. **Step 7**: Create temporary partitioned table for migration
6. **Step 8**: Create initial partitions (current month + 6 months)
7. **Step 9**: Migrate existing data with proper timezone conversion
8. **Step 10**: Replace main table and fix sequence ownership
9. **Step 11**: Optional cleanup (backup table preserved for safety)
10. **Step 12**: Create cleanup function for future maintenance

### Performance Optimizations

#### Indexing Strategy

Each partition automatically receives the following indexes:

```sql
-- Model name and timestamp for time-based queries
CREATE INDEX idx_logs_YYYY_MM_model_timestamp 
ON surge_computation_model_api_logs_YYYY_MM (model_name, create_timestamp DESC);

-- Status code for error analysis
CREATE INDEX idx_logs_YYYY_MM_status 
ON surge_computation_model_api_logs_YYYY_MM (status_code);

-- Model ID for model-specific queries
CREATE INDEX idx_logs_YYYY_MM_model_id 
ON surge_computation_model_api_logs_YYYY_MM (model_id);
```

#### Query Optimization

- **Partition Elimination**: Queries with `singapore_month` constraints automatically target specific partitions
- **Time-based Queries**: 50-80% performance improvement for date range queries
- **Maintenance Operations**: Significantly faster VACUUM, REINDEX operations
- **Constraint Exclusion**: Enabled by default (`constraint_exclusion = partition`)

### Usage Guidelines

#### Recommended Query Patterns

```sql
-- Best Performance: Query with partition key
SELECT * FROM surge_computation_model_api_logs 
WHERE singapore_month = '2024-06-01'::date
AND model_name = 'surge_model_v1'
ORDER BY create_timestamp DESC;

-- Good Performance: Time range with explicit singapore_month
SELECT * FROM surge_computation_model_api_logs 
WHERE singapore_month BETWEEN '2024-01-01'::date AND '2024-06-01'::date
AND status_code >= 400;

-- Standard Performance: Time-based queries (scans multiple partitions)
SELECT model_name, status_code, COUNT(*) 
FROM surge_computation_model_api_logs 
WHERE create_timestamp BETWEEN '2024-01-01 00:00:00'::timestamp AND '2024-01-31 23:59:59'::timestamp
AND status_code >= 400
GROUP BY model_name, status_code;

-- Analyze response patterns for specific model
SELECT 
    DATE_TRUNC('hour', create_timestamp AT TIME ZONE 'Asia/Singapore') as singapore_hour,
    AVG((response_body->>'processing_time')::numeric) as avg_processing_time
FROM surge_computation_model_api_logs 
WHERE model_id = 123
AND create_timestamp >= (NOW() AT TIME ZONE 'UTC')::timestamp - INTERVAL '7 days'
GROUP BY singapore_hour
ORDER BY singapore_hour;
```

#### Best Practices

1. **Include partition key when possible** (`singapore_month`) for optimal performance
2. **Use UTC for create_timestamp comparisons** as data is stored in UTC
3. **Convert to Singapore timezone for business logic** using `AT TIME ZONE 'Asia/Singapore'`
4. **Monitor partition sizes** using provided monitoring functions
5. **Avoid cross-partition joins** when possible

### Data Integrity and Constraints

#### Check Constraints
- **singapore_month Validation**: Ensures `singapore_month` matches the Singapore timezone month derived from `create_timestamp`
- **Automatic Validation**: Constraint prevents data inconsistencies during insert/update operations

#### UTC Timezone Handling
- **Storage**: All timestamps stored in UTC (`create_timestamp TIMESTAMP`)
- **Default Value**: `DEFAULT (now() at time zone 'utc')::timestamp`
- **Partitioning**: Converted to Singapore timezone only for partition key calculation
- **Consistency**: Ensures accurate time-based queries across different timezones

### Monitoring and Maintenance

#### Partition Status Monitoring

```sql
-- Check partition information
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'surge_computation_model_api_logs_%'
ORDER BY tablename;

-- Verify partition constraints
SELECT 
    conname,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'surge_computation_model_api_logs'::regclass;
```

#### Maintenance Operations

```sql
-- Create additional partitions manually
SELECT create_monthly_partition_for_logs('2024-12-01'::date);

-- Ensure future partitions exist
SELECT ensure_monthly_partitions_exist(6);

-- Clean up old partitions (older than 24 months)
SELECT cleanup_old_log_partitions(24);
```

#### Health Checks

```java
// Get partition information
List<PartitionInfo> partitions = partitionManagementService.getPartitionInfo();

// Manually trigger partition creation
String result = partitionManagementService.createPartitionsManually(6);
```

- **Partition Existence**: Verify required partitions exist
- **Size Monitoring**: Track partition growth patterns
- **Performance Metrics**: Monitor query execution times
- **Error Rates**: Track partition creation/cleanup failures

### Migration and Deployment

#### Database Migration

The table conversion is handled by two sequential Liquibase changesets:

1. **`01_alter_surge_computation_model_api_logs_timestamp.sql`**:
   - Renames `timestamp` column to `create_timestamp`
   - Simple column rename operation

2. **`02_standard_convert_to_partitioned_table.sql`**:
   - Creates all partition management functions
   - Converts table to partitioned structure
   - Migrates existing data
   - Sets up automatic partition creation

#### Pre-Migration Checklist
- [ ] Backup existing data
- [ ] Verify PostgreSQL version compatibility (10+)
- [ ] Check available disk space (2x current table size)
- [ ] Schedule maintenance window
- [ ] Verify timezone configuration

#### Post-Migration Validation
- [ ] Verify all data migrated successfully
- [ ] Check partition creation functionality
- [ ] Test query performance with partition elimination
- [ ] Validate constraint checks
- [ ] Confirm sequence ownership and defaults

#### Rollback Support

Full rollback capability is provided in the changeset comments:
```sql
--rollback ALTER TABLE surge_computation_model_api_logs DROP CONSTRAINT IF EXISTS chk_singapore_month_matches_timestamp;
--rollback DROP FUNCTION IF EXISTS cleanup_old_log_partitions(INTEGER);
--rollback DROP FUNCTION IF EXISTS ensure_monthly_partitions_exist(INTEGER);
--rollback DROP FUNCTION IF EXISTS create_monthly_partition_for_logs(DATE, TEXT);
--rollback DROP FUNCTION IF EXISTS get_singapore_month(TIMESTAMP);
--rollback DROP TABLE IF EXISTS surge_computation_model_api_logs;
--rollback ALTER TABLE surge_computation_model_api_logs_backup RENAME TO surge_computation_model_api_logs;
```

### Technical Requirements

#### PostgreSQL Version Compatibility

- **Minimum Version**: PostgreSQL 10+ (for declarative partitioning)
- **Recommended**: PostgreSQL 14+ (current production: 14.15)
- **Tested On**: PostgreSQL 14.15

#### Feature Dependencies

- **Declarative Partitioning**: PostgreSQL 10+
- **JSONB Support**: PostgreSQL 9.4+
- **Timezone Handling**: All PostgreSQL versions
- **Constraint Exclusion**: All PostgreSQL versions

### Future Enhancements

#### Potential Improvements

1. **Compression**: Implement table compression for older partitions
2. **Archival**: Automatic archival to cold storage
3. **Advanced Analytics**: Real-time metrics and dashboards
4. **Cross-Region Replication**: Multi-region data distribution
5. **Automatic Partition Pruning**: Intelligent partition elimination based on query patterns

#### Scalability Considerations

- **Partition Size**: Target 1-5 million rows per partition
- **Query Performance**: Monitor and optimize based on usage patterns
- **Storage Growth**: Plan for approximately 10-50GB per month (depending on volume)
- **Index Maintenance**: Regular REINDEX operations on older partitions

### Support and Troubleshooting

#### Common Issues

1. **Missing Partitions**: 
   - **Symptom**: Insert fails with "no partition of relation found"
   - **Solution**: Run `SELECT ensure_monthly_partitions_exist(3);`

2. **Performance Degradation**: 
   - **Symptom**: Queries scanning all partitions
   - **Solution**: Include `singapore_month` in WHERE clause for partition elimination

3. **Storage Growth**: 
   - **Symptom**: Disk space issues with old partitions
   - **Solution**: Run `SELECT cleanup_old_log_partitions(24);`

4. **Sequence Issues**: 
   - **Symptom**: `ERROR: null value in column "id" violates not-null constraint`
   - **Cause**: Partition doesn't inherit sequence default
   - **Solution**: Check and fix partition sequence defaults in the migration script

5. **Timezone Confusion**:
   - **Symptom**: Data appears in wrong partition
   - **Cause**: Timezone conversion issues
   - **Solution**: Ensure UTC input and verify `get_singapore_month()` function

6. **Constraint Violations**:
   - **Symptom**: `ERROR: violates check constraint "chk_singapore_month_matches_timestamp"`
   - **Cause**: Manual data insertion with incorrect singapore_month
   - **Solution**: Let the default value calculate singapore_month automatically

#### Contact Information

For technical support or questions about this table:
- **Development Team**: Dynamic Pricing Service Team
- **Database Team**: Platform Engineering
- **Documentation**: This file and related Liquibase changesets

---

*Last Updated: December 2024*  
*Version: 2.0*  
*Related Changesets: 01_alter_surge_computation_model_api_logs_timestamp.sql, 02_standard_convert_to_partitioned_table.sql*  
*Major Updates: Updated for partitioned table implementation with UTC timestamp storage and Singapore timezone partitioning* 