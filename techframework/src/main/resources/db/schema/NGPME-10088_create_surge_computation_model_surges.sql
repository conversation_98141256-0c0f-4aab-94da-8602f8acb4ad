--liquibase formatted sql
--changeset <EMAIL>:NGPME-10088_create_surge_computation_model_surges.sql splitStatements:false
--create surge_computation_model_surges table

CREATE TABLE IF NOT EXISTS surge_computation_model_surges
(
    id          BIGSERIAL PRIMARY KEY,
    model_id    BIGINT       NOT NULL
        CONSTRAINT FK_surge_computation_models_id REFERENCES surge_computation_models (id),
    region_id   BIGINT       NOT NULL,
    prev_surge  NUMERIC      NULL,
    surge       NUMERIC      NOT NULL,
    last_upd_dt TIMESTAMP    NOT NULL,
    UNIQUE (model_id, region_id)
);

COMMENT ON TABLE surge_computation_model_surges is 'Table to store surge computation model surges';
COMMENT ON COLUMN surge_computation_model_surges.id is 'Primary key for the surge computation model surges';
COMMENT ON COLUMN surge_computation_model_surges.model_id is 'The model id of the surge computation model';
COMMENT ON COLUMN surge_computation_model_surges.region_id is 'The H3 region id from ngp_me_address.h3_region_definitions';
COMMENT ON COLUMN surge_computation_model_surges.prev_surge is 'The previous surge value for this region';
COMMENT ON COLUMN surge_computation_model_surges.surge is 'The surge value returned by the surge computation model service';
COMMENT ON COLUMN surge_computation_model_surges.last_upd_dt is 'The last updated datetime of this record';
COMMENT ON CONSTRAINT FK_surge_computation_models_id ON surge_computation_model_surges IS 'Foreign key constraint to surge_computation_models table';


--rollback DROP TABLE IF EXISTS surge_computation_model_surges;