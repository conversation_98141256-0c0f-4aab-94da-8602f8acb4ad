--liquibase formatted sql
--changeset <EMAIL>:NGPME-10088_create_surge_computation_model_api_logs.sql splitStatements:false
--create surge_computation_model_api_logs table

CREATE TABLE IF NOT EXISTS surge_computation_model_api_logs
(
    id             BIGSERIAL PRIMARY KEY,
    model_id       BIGINT       NOT NULL,
    model_name     VARCHAR(255) NOT NULL,
    endpoint_url   VARCHAR(255) NOT NULL,
    timestamp      TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    status_code    INT          NOT NULL,
    request_params JSONB        NOT NULL,
    response_body  JSONB        NOT NULL
);

COMMENT ON TABLE surge_computation_model_api_logs is 'Table to store surge computation model API logs';
COMMENT ON COLUMN surge_computation_model_api_logs.id is 'Primary key for the surge computation model api logs';
COMMENT ON COLUMN surge_computation_model_api_logs.model_id is 'The model id of the surge computation model';
COMMENT ON COLUMN surge_computation_model_api_logs.model_name is 'The model name of the surge computation model';
COMMENT ON COLUMN surge_computation_model_api_logs.endpoint_url is 'The endpoint url of the surge computation model';
COMMENT ON COLUMN surge_computation_model_api_logs.timestamp is 'The Time of API request';
COMMENT ON COLUMN surge_computation_model_api_logs.status_code is 'HTTP status code of the response';
COMMENT ON COLUMN surge_computation_model_api_logs.request_params is 'JSONB for storing dynamic request parameters';
COMMENT ON COLUMN surge_computation_model_api_logs.response_body is 'JSONB for storing dynamic response data';

CREATE INDEX idx_surge_logs_model_name_timestamp ON surge_computation_model_api_logs (model_name, timestamp desc); -- API Name Index: Allows fast filtering by model name and timestamp.
CREATE INDEX idx_surge_logs_status_code ON surge_computation_model_api_logs (status_code); -- Status Code Index: Useful for filtering logs by status (e.g., failures).


--rollback DROP TABLE IF EXISTS surge_computation_model_api_logs;