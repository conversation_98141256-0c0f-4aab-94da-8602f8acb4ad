--liquibase formatted sql
--changeset NGPME-10089_convert_to_partitioned_table.sql splitStatements:false
--convert surge_computation_model_api_logs to partitioned table based on Singapore timezone months

-- Step 1: Create Singapore month function
CREATE OR REPLACE FUNCTION get_singapore_month(ts TIMESTAMP)
RETURNS DATE AS $$
BEGIN
    RETURN DATE_TRUNC('month', ts AT TIME ZONE 'Asia/Singapore')::DATE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Step 2: Create partition function
CREATE OR REPLACE FUNCTION create_monthly_partition_for_logs(target_month DATE, table_name TEXT DEFAULT 'surge_computation_model_api_logs')
RETURNS TEXT AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
    sql_stmt TEXT;
BEGIN
    start_date := DATE_TRUNC('month', target_month);
    end_date := start_date + INTERVAL '1 month';
    partition_name := 'surge_computation_model_api_logs_' || TO_CHAR(start_date, 'YYYY_MM');

    -- Create partition
    sql_stmt := format(
        'CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
         FOR VALUES FROM (%L) TO (%L)',
        partition_name, table_name, start_date, end_date
    );
    EXECUTE sql_stmt;

    -- Note: Partitions inherit defaults automatically

    -- Create indexes
    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_model_timestamp
         ON %I (model_name, create_timestamp DESC)',
        partition_name, partition_name
    );

    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_status
         ON %I (status_code)',
        partition_name, partition_name
    );

    EXECUTE format(
        'CREATE INDEX IF NOT EXISTS idx_%s_model_id
         ON %I (model_id)',
        partition_name, partition_name
    );

    RETURN partition_name;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create partition management function
CREATE OR REPLACE FUNCTION ensure_monthly_partitions_exist(months_ahead INTEGER DEFAULT 3)
RETURNS TEXT[] AS $$
DECLARE
    singapore_current_date DATE;
    target_month DATE;
    i INTEGER;
    created_partitions TEXT[] := '{}';
    partition_name TEXT;
BEGIN
    singapore_current_date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE;

    FOR i IN 0..months_ahead LOOP
        target_month := DATE_TRUNC('month', singapore_current_date) + (i || ' month')::INTERVAL;

        IF NOT EXISTS (
            SELECT 1 FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relname = 'surge_computation_model_api_logs_' || TO_CHAR(target_month, 'YYYY_MM')
            AND n.nspname = current_schema()
        ) THEN
            partition_name := create_monthly_partition_for_logs(target_month);
            created_partitions := array_append(created_partitions, partition_name);
        END IF;
    END LOOP;

    RETURN created_partitions;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Backup table and create sequence
ALTER TABLE surge_computation_model_api_logs RENAME TO surge_computation_model_api_logs_backup;

CREATE SEQUENCE IF NOT EXISTS surge_computation_model_api_logs_id_seq AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

-- Step 5: Create partitioned table
CREATE TABLE surge_computation_model_api_logs (
    id             BIGINT NOT NULL DEFAULT nextval('surge_computation_model_api_logs_id_seq'),
    model_id       BIGINT       NOT NULL,
    model_name     VARCHAR(255) NOT NULL,
    endpoint_url   VARCHAR(255) NOT NULL,
    create_timestamp TIMESTAMP    NOT NULL DEFAULT (now() at time zone 'utc')::timestamp,
    status_code    INT          NOT NULL,
    request_params JSONB        NOT NULL,
    response_body  JSONB        NOT NULL,
    singapore_month DATE NOT NULL DEFAULT (get_singapore_month((now() at time zone 'utc')::timestamp)),
    PRIMARY KEY (id, singapore_month)
) PARTITION BY RANGE (singapore_month);

-- Step 6: Add comments
COMMENT ON TABLE surge_computation_model_api_logs IS 'Partitioned table for surge computation model API logs by Singapore timezone months';
COMMENT ON COLUMN surge_computation_model_api_logs.id IS 'Primary key';
COMMENT ON COLUMN surge_computation_model_api_logs.model_id IS 'Surge computation model ID';
COMMENT ON COLUMN surge_computation_model_api_logs.model_name IS 'Surge computation model name';
COMMENT ON COLUMN surge_computation_model_api_logs.endpoint_url IS 'API endpoint URL';
COMMENT ON COLUMN surge_computation_model_api_logs.create_timestamp IS 'API request timestamp (UTC)';
COMMENT ON COLUMN surge_computation_model_api_logs.status_code IS 'HTTP response status code';
COMMENT ON COLUMN surge_computation_model_api_logs.request_params IS 'Request parameters (JSON)';
COMMENT ON COLUMN surge_computation_model_api_logs.response_body IS 'Response data (JSON)';
COMMENT ON COLUMN surge_computation_model_api_logs.singapore_month IS 'Partition key: Singapore timezone month';

-- Step 7: Create temporary table for migration
CREATE TABLE surge_computation_model_api_logs_partitioned (LIKE surge_computation_model_api_logs INCLUDING ALL) PARTITION BY RANGE (singapore_month);

-- Add data integrity constraint
ALTER TABLE surge_computation_model_api_logs_partitioned 
ADD CONSTRAINT chk_singapore_month_matches_timestamp 
CHECK (singapore_month = get_singapore_month(create_timestamp));

-- Step 8: Create initial partitions
DO $$
DECLARE
    singapore_current_date DATE;
    target_month DATE;
    i INTEGER;
BEGIN
    singapore_current_date := (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE;
    
    FOR i IN 0..6 LOOP
        target_month := DATE_TRUNC('month', singapore_current_date) + (i || ' month')::INTERVAL;
        PERFORM create_monthly_partition_for_logs(target_month, 'surge_computation_model_api_logs_partitioned');
    END LOOP;
END $$;

-- Step 9: Migrate data
INSERT INTO surge_computation_model_api_logs_partitioned (
    id, model_id, model_name, endpoint_url, create_timestamp, status_code, request_params, response_body, singapore_month
)
SELECT 
    id, model_id, model_name, endpoint_url, create_timestamp, status_code, request_params, response_body,
    get_singapore_month(create_timestamp) as singapore_month
FROM surge_computation_model_api_logs_backup
ON CONFLICT (id, singapore_month) DO NOTHING;

-- Step 10: Replace table
DROP TABLE IF EXISTS surge_computation_model_api_logs CASCADE;
ALTER TABLE surge_computation_model_api_logs_partitioned RENAME TO surge_computation_model_api_logs;

-- Transfer sequence ownership
ALTER SEQUENCE surge_computation_model_api_logs_id_seq OWNED BY surge_computation_model_api_logs.id;

-- Step 11: Cleanup (commented for safety)
-- DROP TABLE IF EXISTS surge_computation_model_api_logs_backup CASCADE;

-- Step 12: Create cleanup function
CREATE OR REPLACE FUNCTION cleanup_old_log_partitions(months_to_keep INTEGER DEFAULT 24)
RETURNS TEXT[] AS $$
DECLARE
    cutoff_date DATE;
    partition_record RECORD;
    dropped_partitions TEXT[] := '{}';
BEGIN
    cutoff_date := DATE_TRUNC('month', (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Singapore')::DATE - (months_to_keep || ' months')::INTERVAL);

    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'surge_computation_model_api_logs_%'
        AND tablename ~ '^surge_computation_model_api_logs_\d{4}_\d{2}$'
        AND TO_DATE(SUBSTRING(tablename FROM '\d{4}_\d{2}'), 'YYYY_MM') < cutoff_date
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(partition_record.schemaname) || '.' || quote_ident(partition_record.tablename);
        dropped_partitions := array_append(dropped_partitions, partition_record.tablename);
    END LOOP;

    RETURN dropped_partitions;
END;
$$ LANGUAGE plpgsql;

--rollback ALTER TABLE surge_computation_model_api_logs DROP CONSTRAINT IF EXISTS chk_singapore_month_matches_timestamp;
--rollback DROP FUNCTION IF EXISTS cleanup_old_log_partitions(INTEGER);
--rollback DROP FUNCTION IF EXISTS ensure_monthly_partitions_exist(INTEGER);
--rollback DROP FUNCTION IF EXISTS create_monthly_partition_for_logs(DATE, TEXT);
--rollback DROP FUNCTION IF EXISTS get_singapore_month(TIMESTAMP);
--rollback DROP TABLE IF EXISTS surge_computation_model_api_logs;
--rollback ALTER TABLE surge_computation_model_api_logs_backup RENAME TO surge_computation_model_api_logs;