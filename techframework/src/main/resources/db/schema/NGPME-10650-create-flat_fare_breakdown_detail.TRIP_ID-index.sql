--liquibase formatted sql
--changeset <EMAIL>:NGPME-10650-create-flat_fare_breakdown_detail.TRIP_ID-index.sql runInTransaction:false
--create the index for flat_fare_breakdown_detail table

-- Add index for flat_fare_breakdown_detail column TRIP_ID
-- Removed hardcoded schema reference for better portability and backward compatibility
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_flat_fare_breakdown_detail_trip_id
    ON flat_fare_breakdown_detail (TRIP_ID);

-- rollback DROP INDEX IF EXISTS idx_flat_fare_breakdown_detail_trip_id;