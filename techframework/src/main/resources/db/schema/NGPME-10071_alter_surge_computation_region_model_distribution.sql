--liquibase formatted sql
--changeset <EMAIL>:NGPME-10071_alter_surge_computation_region_model_distribution.sql splitStatements:false

CREATE OR REPLACE FUNCTION populate_effective_to_before_insert_model_distribution() RETURNS TRIGGER AS $$
BEGIN
    UPDATE surge_computation_region_model_distribution
    SET effective_to = NEW.effective_from - INTERVAL '1 SECOND'
    WHERE region_id = NEW.region_id
      AND effective_to IS NULL
      AND effective_from < NEW.effective_from;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS before_insert_model_distribution_trigger ON surge_computation_region_model_distribution;
CREATE TRIGGER before_insert_model_distribution_trigger
    BEFORE INSERT
    ON surge_computation_region_model_distribution
    FOR EACH ROW
EXECUTE FUNCTION populate_effective_to_before_insert_model_distribution();

--rollback DROP FUNCTION IF EXISTS populate_effective_to_before_insert_static_region_config();
--rollback DROP TRIGGER IF EXISTS before_insert_static_region_config_trigger ON surge_computation_region_model_distribution;