--liquibase formatted sql
--changeset <EMAIL>:NGPME-9995_create_surge_computation_model.sql splitStatements:false
--create surge_computation_models table and audit table with triggers

-- Create the main table
CREATE TABLE IF NOT EXISTS surge_computation_models
(
    id                      BIGINT       not null
        constraint PK_SURGE_COMPUTATION_MODELS
            primary key,
    model_name              <PERSON><PERSON><PERSON><PERSON>(255) not null,
    description             TEXT,
    endpoint_url            VARCHAR(255),
    request_fields_mappings JSONB,
    created_by              <PERSON><PERSON><PERSON><PERSON>(255) not null,
    created_dt              TIMESTAMP    not null,
    updated_by              <PERSON><PERSON><PERSON><PERSON>(255),
    updated_dt              TIMESTAMP,
    CONSTRAINT unique_model_name UNIQUE (model_name)
);

COMMENT ON TABLE surge_computation_models is 'Table to store surge computation models information';
COMMENT ON COLUMN surge_computation_models.id is 'Primary key for the surge computation model';
COMMENT ON COLUMN surge_computation_models.model_name is 'Name of the surge computation model';
COMMENT ON COLUMN surge_computation_models.description is 'Description of the surge computation model';
COMMENT ON COLUMN surge_computation_models.endpoint_url is 'URL endpoint for the surge computation model';
COMMENT ON COLUMN surge_computation_models.request_fields_mappings is 'JSON array of mappings between request parameters and configuration names. Each mapping contains mapping_type, request_parameter_name, and mapping_configuration_name.';
COMMENT ON COLUMN surge_computation_models.created_by is 'User who created the record';
COMMENT ON COLUMN surge_computation_models.created_dt is 'Date and time when the record was created';
COMMENT ON COLUMN surge_computation_models.updated_by is 'User who last updated the record';
COMMENT ON COLUMN surge_computation_models.updated_dt is 'Date and time when the record was last updated';

-- Set default values for created_dt and created_by
ALTER TABLE surge_computation_models
    ALTER COLUMN created_dt SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE surge_computation_models
    ALTER COLUMN created_by SET DEFAULT 'SYSTEM';

-- Create the sequence for the primary key
CREATE SEQUENCE IF NOT EXISTS SEQ_surge_computation_models__id AS BIGINT
    INCREMENT 1
    START 1
    MINVALUE 1
    CACHE 1;

ALTER TABLE surge_computation_models
    ALTER COLUMN id SET DEFAULT nextval('SEQ_surge_computation_models__id');

-- Create a GIN index for the JSONB column to improve query performance
CREATE INDEX idx_surge_computation_models_gin
    ON surge_computation_models USING GIN (request_fields_mappings);


-- Create the audit table
CREATE TABLE IF NOT EXISTS surge_computation_models_audit
(
    audit_id                BIGSERIAL PRIMARY KEY,
    operation_type          CHAR(1)   NOT NULL,
    operation_timestamp     TIMESTAMP NOT NULL,
    operation_user          VARCHAR(255),
    id                      BIGINT,
    model_name              VARCHAR(255),
    description             TEXT,
    endpoint_url            VARCHAR(255),
    request_fields_mappings JSONB,
    created_by              VARCHAR(255),
    created_dt              TIMESTAMP,
    updated_by              VARCHAR(255),
    updated_dt              TIMESTAMP
);

COMMENT ON TABLE surge_computation_models_audit is 'Audit table for surge_computation_models';
COMMENT ON COLUMN surge_computation_models_audit.audit_id is 'Primary key for the audit record';
COMMENT ON COLUMN surge_computation_models_audit.operation_type is 'Type of operation (I=Insert, U=Update, D=Delete)';
COMMENT ON COLUMN surge_computation_models_audit.operation_timestamp is 'Timestamp when the operation occurred';
COMMENT ON COLUMN surge_computation_models_audit.operation_user is 'User who performed the operation';

-- Create the audit trigger
CREATE OR REPLACE PROCEDURE insert_surge_computation_models_audit(data surge_computation_models, action text, schema_name text)
    LANGUAGE plpgsql
AS
$$
BEGIN
    EXECUTE format('
    INSERT INTO %I.surge_computation_models_audit(
        operation_type,
        operation_timestamp,
        operation_user,
        id,
        model_name,
        description,
        endpoint_url,
        request_fields_mappings,
        created_by,
        created_dt,
        updated_by,
        updated_dt
    )
    VALUES(
      $1,
      NOW(),
      $2.updated_by,
      $2.id,
      $2.model_name,
      $2.description,
      $2.endpoint_url,
      $2.request_fields_mappings,
      $2.created_by,
      $2.created_dt,
      $2.updated_by,
      $2.updated_dt
    );
    ', schema_name) USING action, data;
END;
$$;

-- Function creation
CREATE OR REPLACE FUNCTION audit_surge_computation_models() RETURNS TRIGGER AS
$$
DECLARE
    current_schema_name text;
BEGIN
    current_schema_name := TG_TABLE_SCHEMA;

    IF (TG_OP = 'DELETE') THEN
        EXECUTE 'CALL insert_surge_computation_models_audit($1, ''D'', $2);' USING OLD, current_schema_name;
    ELSIF (TG_OP = 'INSERT') THEN
        EXECUTE 'CALL insert_surge_computation_models_audit($1, ''I'', $2);' USING NEW, current_schema_name;
    ELSIF (TG_OP = 'UPDATE') THEN
        EXECUTE 'CALL insert_surge_computation_models_audit($1, ''U'', $2);' USING NEW, current_schema_name;
    END IF;

    RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$$ LANGUAGE plpgsql;


-- Create the trigger
DROP TRIGGER IF EXISTS audit_surge_computation_models_trigger ON surge_computation_models;
CREATE TRIGGER audit_surge_computation_models_trigger
    AFTER INSERT OR UPDATE OR DELETE
    ON surge_computation_models
    FOR EACH ROW
EXECUTE FUNCTION audit_surge_computation_models();

--rollback DROP TRIGGER IF EXISTS audit_surge_computation_models_trigger ON surge_computation_models;
--rollback DROP FUNCTION IF EXISTS audit_surge_computation_models();
--rollback DROP PROCEDURE IF EXISTS insert_surge_computation_models_audit(surge_computation_models, text, text);
--rollback DROP TABLE IF EXISTS surge_computation_models_audit;
--rollback ALTER TABLE surge_computation_models ALTER COLUMN id DROP DEFAULT;
--rollback DROP SEQUENCE IF EXISTS SEQ_surge_computation_models__id;
--rollback DROP TABLE IF EXISTS surge_computation_models;
