# clone from uat1-r2
# AWS Parameter Store integration, will not create r2 properties in workload non prod
spring.config.import[0]=optional:aws-parameterstore:/config/ngp-me-dynamicpricing-svc_uat1/
# AWS Secrets Manager integration
spring.config.import[1]=optional:aws-secretsmanager:/secrets/ngp/uat1;/secrets/ngp/uat1/ngp-me-dynamicpricing-svc
# Specify the URL of the Config Server to get properties
spring.config.import[2]=optional:configserver:https://common-cms-uat1.internal.eks.sg.nprod.zig.systems/v1.0/config-server

# RELEASE VERSION
dps.system.param.applicationRelease=2