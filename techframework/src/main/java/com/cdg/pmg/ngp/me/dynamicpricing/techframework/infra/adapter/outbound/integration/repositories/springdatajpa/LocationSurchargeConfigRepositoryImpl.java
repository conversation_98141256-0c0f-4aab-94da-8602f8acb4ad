package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.LocationSurchargeConfigRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeAddressEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocSurchargeEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.LocationSurchargeConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.LocationSurchargeConfigQueryResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.LocationSurchargeJPACustom;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.LocationSurchargeJPARepository;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class LocationSurchargeConfigRepositoryImpl implements LocationSurchargeConfigRepository {
  private static final int LIMIT_PAGING = 30000;
  private final LocationSurchargeJPARepository locationSurchargeJPARepository;

  @Override
  public List<LocSurchargeEntity> getLocSurchargeConfigs() {
    List<LocSurchargeEntity> result = new ArrayList<>();
    try {
      result = locationSurchargeJPARepository.getLocSurcharge();
    } catch (Exception exception) {
      log.error("getLocSurchargeConfigs error: {}", exception.getMessage());
    }
    return result;
  }

  @Override
  public List<LocSurchargeAddressEntity> getLocSurchargeAddresses() {
    List<LocSurchargeAddressEntity> result = new ArrayList<>();
    try {
      result = locationSurchargeJPARepository.getLocSurchargeAddresses();
    } catch (Exception exception) {
      log.error("getLocSurchargeAddresses error: {}", exception.getMessage());
    }

    return result;
  }

  @Override
  public LocationSurchargeConfigQueryResponse getLocationSurchargeConfigs(int page) {
    final var locationSurchargeConfigQueryResponse = new LocationSurchargeConfigQueryResponse();

    try {
      final int offset = page * LIMIT_PAGING;
      List<LocationSurchargeJPACustom> locConfigs =
          locationSurchargeJPARepository.getLocationSurchargeConfigs(LIMIT_PAGING, offset);
      log.info("Location surcharge size: {}", locConfigs.size());

      locationSurchargeConfigQueryResponse.setConfigs(
          locConfigs.stream()
              .map(
                  conf ->
                      LocationSurchargeConfigEntity.builder()
                          .locationId(conf.getLocationId())
                          .locationName(conf.getLocationName())
                          .addressRef(conf.getAddressRef())
                          .fareType(conf.getFareType())
                          .chargeBy(conf.getChargeBy())
                          .surchargeValue(conf.getSurchargeValue())
                          .productId(conf.getProductId())
                          .startTime(conf.getStartTime())
                          .endTime(conf.getEndTime())
                          .dayIndicator(conf.getDayIndicator())
                          .build())
              .toList());

      log.info("getLocationSurchargeConfigs got data from page {}", page);
    } catch (Exception exception) {
      log.error("getLocationSurchargeConfigs error: {}", exception.getMessage());
    }

    return locationSurchargeConfigQueryResponse;
  }
}
