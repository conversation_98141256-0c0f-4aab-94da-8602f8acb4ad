package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.DemandSupplyStatisticsOutboundV2Response;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.GetLatestRegionDemandSupplyStatisticsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.GetRegionDemandSupplyStatisticRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/** The interface Fleet Analytic outbound adapter. */
@FeignClient(name = "${openfeign.fleetAnalyticClient.name}")
public interface FleetAnalyticOutboundAdapter {

  /**
   * Get demand supply statistic from Fleet Analytic Svc
   *
   * @return response
   */
  @GetMapping(
      value = "/v1.0/fleet-analytic/demand-supply-statistics",
      consumes = "application/json")
  ResponseEntity<DemandSupplyStatisticsOutboundResponse> getDemandSupplyStatistics();

  @GetMapping(
      value = "/v2.0/fleet-analytic/demand-supply-statistics",
      consumes = "application/json")
  ResponseEntity<DemandSupplyStatisticsOutboundV2Response> getDemandSupplyStatisticsV2();

  @PostMapping(
      value = "/v1.0/fleet-analytic/region/demand-supply-statistics/search",
      consumes = "application/json",
      produces = "application/json")
  ResponseEntity<GetLatestRegionDemandSupplyStatisticsResponse>
      getLatestRegionDemandSupplyStatistics(
          @RequestBody(required = false) GetRegionDemandSupplyStatisticRequest request);
}
