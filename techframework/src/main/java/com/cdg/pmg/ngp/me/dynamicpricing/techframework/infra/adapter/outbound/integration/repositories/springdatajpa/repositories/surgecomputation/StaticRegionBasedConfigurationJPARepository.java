package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticRegionBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for StaticRegionBasedConfigurationJPA entity. Provides methods for CRUD
 * operations on static region-based configurations. Maps to the
 * surge_computation_static_region_based_configurations table.
 */
@Repository
public interface StaticRegionBasedConfigurationJPARepository
    extends JpaRepository<StaticRegionBasedConfigurationJPA, Long> {

  /**
   * Find all static region-based configurations that are effective at a specific time.
   *
   * @param effectiveTime the time at which the configurations should be effective
   * @return a list of static region-based configurations
   */
  @Query(
      "SELECT c FROM StaticRegionBasedConfigurationJPA c WHERE c.effectiveFrom <= :effectiveTime "
          + "AND (c.effectiveTo IS NULL OR c.effectiveTo > :effectiveTime)")
  List<StaticRegionBasedConfigurationJPA> findByEffectiveTimeRange(
      @Param("effectiveTime") Instant effectiveTime);

  /**
   * Find the most recent effective_from value by the given effectiveTime.
   *
   * @param effectiveTime the time at which the configurations should be effective
   * @return the most recent effective_from value
   */
  @Query(
      value =
          """
        select effective_from
        from surge_computation_static_region_based_configurations
        where effective_from <= :effectiveTime
        order by effective_from desc
        limit 1
    """,
      nativeQuery = true)
  Instant findRecentEffectiveFromByEffectiveFrom(@Param("effectiveTime") Instant effectiveTime);

  @Query(
      value =
          """
    SELECT
        version,
        effective_from as effectiveFrom,
        effective_to as effectiveTo
    FROM surge_computation_static_region_based_configurations
    WHERE name in (:configNames)
        GROUP BY version, effective_from, effective_to
        ORDER BY version DESC
    """,
      nativeQuery = true)
  List<StaticBasedConfigurationVersionProjection> findAllVersionsDescByNames(
      List<String> configNames);

  List<StaticRegionBasedConfigurationJPA> findAllByVersionAndNameIn(
      String version, List<String> names);

  List<StaticRegionBasedConfigurationJPA> findAllByNameIn(List<String> names);

  @Modifying
  @Query(
      value =
          """
        update surge_computation_static_region_based_configurations
            set updated_by = :updatedBy,
                updated_dt = :updatedDate
        where name in (:configNames)
    """,
      nativeQuery = true)
  void updateAuditFields(List<String> configNames, String updatedBy, Instant updatedDate);

  @Modifying
  @Query(
      value =
          """
        delete from surge_computation_static_region_based_configurations
        where name in (:configNames)
    """,
      nativeQuery = true)
  void deleteByNames(List<String> configNames);

  @Query(
      value =
          """
    SELECT
        version,
        effective_from as effectiveFrom,
        effective_to as effectiveTo
    FROM surge_computation_static_region_based_configurations
    WHERE effective_from <= :effectiveTime
        AND (effective_to IS NULL OR effective_to > :effectiveTime)
        GROUP BY version, effective_from, effective_to
    """,
      nativeQuery = true)
  StaticBasedConfigurationVersionProjection findEffectiveVersions(
      @Param("effectiveTime") Instant effectiveTime);
}
