package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.CompanyHolidayJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CompanyHolidayJPARepository extends JpaRepository<CompanyHolidayJPA, String> {
  @Query(
      "SELECT h.phDate "
          + "FROM CompanyHolidayJPA as h "
          + "WHERE TO_CHAR(h.phDate, 'YYYY') = TO_CHAR(current_timestamp, 'YYYY')")
  List<String> getCompanyHolidays();

  @Query(
      value =
          "SELECT ph_date "
              + "FROM COMPANY_HOLIDAY "
              + "WHERE DATE_TRUNC('DAY', ph_date) = current_date ",
      nativeQuery = true)
  String getCurrentHoliday();

  @Query(
      value =
          """
          SELECT ph_date from company_holiday
          WHERE date_trunc('DAY', ph_date) = date_trunc('DAY',TIMEZONE('Asia/Singapore', NOW()));
          """,
      nativeQuery = true)
  String getCurrentHolidayUsingSingTimeZone();
}
