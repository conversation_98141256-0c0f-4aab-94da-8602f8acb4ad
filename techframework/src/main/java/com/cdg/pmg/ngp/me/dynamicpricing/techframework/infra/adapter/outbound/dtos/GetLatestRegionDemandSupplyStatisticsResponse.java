package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class GetLatestRegionDemandSupplyStatisticsResponse implements Serializable {
  @Serial private static final long serialVersionUID = 90214290441484767L;

  private List<RegionDemandSupplyStatisticOutBound> data;
}
