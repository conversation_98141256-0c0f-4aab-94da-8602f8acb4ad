package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationManagementApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for surge computation management operations. This controller implements the
 * SurgeComputationManagementApi interface.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class SurgeComputationManagementController implements SurgeComputationManagementApi {

  private final SurgeFactorCalculationService surgeFactorCalculationService;

  /**
   * Endpoint to trigger the calculation of surge factors. This is an asynchronous operation that
   * initiates the calculation process.
   *
   * @return ResponseEntity with no content (204) if successful, or an error status
   */
  @Override
  public ResponseEntity<Void> calculateSurgeFactor() {
    log.info("Received request to calculate surge factor");

    try {
      boolean initiated = surgeFactorCalculationService.calculateSurgeFactor();

      if (initiated) {
        log.info("Surge factor calculation initiated successfully");
        return ResponseEntity.noContent().build();
      } else {
        log.error("Failed to initiate surge factor calculation");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
      }
    } catch (Exception e) {
      log.error("Error processing surge factor calculation request: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
  }
}
