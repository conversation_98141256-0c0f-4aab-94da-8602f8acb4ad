package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.ModelJPA;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for SurgeComputationModelJPA entity. Provides standard CRUD operations for
 * surge computation models.
 */
@Repository
public interface ModelJPARepository extends JpaRepository<ModelJPA, Long> {
  // Standard CRUD methods are inherited from JpaRepository

  @Query(value = "select id from surge_computation_models where id in (:ids)", nativeQuery = true)
  List<Long> findIdByIds(Collection<Long> ids);

  @Query(
      value =
          """
       SELECT DISTINCT rfm->>'mappingConfigurationName' AS configName
       FROM surge_computation_models
       CROSS JOIN LATERAL jsonb_array_elements(request_fields_mappings) as rfm
       WHERE id != :modelId
       AND rfm @> '{"mappingType": "STATIC_REGION_BASED_CONFIGURATION"}'
       AND rfm->>'mappingConfigurationName' IN :configNames
       """,
      nativeQuery = true)
  List<String> findConfigNamesUsedByOtherModels(List<String> configNames, Long modelId);

  @Query(
      value = "select id from surge_computation_models where model_name = :modelName",
      nativeQuery = true)
  Long findIdByModelName(String modelName);

  @Query(
      value = "select model_name from surge_computation_models where id = :modelId",
      nativeQuery = true)
  String findModelNameById(Long modelId);
}
