package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetLocationSurchargeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetLocationSurchargeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetPickupLocationSurchargeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetPickupLocationSurchargeResponse;
import java.util.List;
import org.mapstruct.Mapper;

@Mapper
public interface LocationSurchargeConfigMapper {
  LocationSurchargeConfigRequest map(GetPickupLocationSurchargeRequest request);

  GetPickupLocationSurchargeResponse map(LocationSurchargeConfig locationSurchargeConfig);

  LocationSurchargeConfigRequest map(GetLocationSurchargeRequest request);

  List<GetLocationSurchargeResponse> map(List<LocationSurchargeConfig> locationSurchargeConfigs);
}
