package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_TO_CONVERTING_TO_JSON;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.ERROR_TO_PARSING_JSON;

import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.configs.properties.RedisAppProperties;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.redis.RedisService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class RedisServiceImpl implements RedisService {

  private static final String ERROR_CONVERTING_MSG = "Error Converting object to json";
  private static final String ERROR_PARSING_MSG = "Error Parsing json to object";

  private final StringRedisTemplate redisTemplate;
  private final ObjectMapper objectMapper;
  private final RedisAppProperties redisAppProperties;

  @Override
  public <T> void setListValue(String key, List<T> data) {
    if (data == null || data.isEmpty()) {
      return;
    }
    final List<String> listDataJson =
        data.stream()
            .filter(Objects::nonNull)
            .map(
                item -> {
                  try {
                    return objectMapper.writeValueAsString(item);
                  } catch (JsonProcessingException e) {
                    log.error(ERROR_TO_CONVERTING_TO_JSON.getMessage(), e);
                    throw new InternalServerException(
                        ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
                  }
                })
            .toList();
    if (!listDataJson.isEmpty()) {
      redisTemplate.opsForList().rightPushAll(key, listDataJson);
    }
  }

  @Override
  public <T> void setValue(@NotNull String key, T data) {
    try {
      var dataJson = objectMapper.writeValueAsString(data);

      redisTemplate.opsForValue().set(key, dataJson);
    } catch (JsonProcessingException e) {
      log.error(ERROR_TO_CONVERTING_TO_JSON.getMessage(), e);
      throw new InternalServerException(
          ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
    }
  }

  @Override
  public <T> void setValue(@NotNull String key, T data, int expireDuration) {
    setValue(key, data, (long) expireDuration);
  }

  @Override
  public <T> void setValue(@NotNull String key, T data, long expireDuration) {
    try {
      var dataJson = objectMapper.writeValueAsString(data);

      redisTemplate.opsForValue().set(key, dataJson, expireDuration, TimeUnit.SECONDS);
    } catch (JsonProcessingException e) {
      log.error(ERROR_CONVERTING_MSG, e);
      throw new InternalServerException(ERROR_PARSING_MSG, 500L);
    }
  }

  @Override
  public <T> T getValue(@NotNull String key, @NotNull Class<T> type) {
    T t = null;
    try {
      var dataJson = redisTemplate.opsForValue().get(key);
      if (StringUtils.isNotEmpty(dataJson)) {
        t = objectMapper.readValue(dataJson, type);
      }
    } catch (JsonProcessingException e) {
      log.error(ERROR_TO_PARSING_JSON.getMessage(), e);
      throw new InternalServerException(
          ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
    }
    return t;
  }

  @Override
  public void deleteByKey(@NotNull String key) {
    redisTemplate.delete(key);
  }

  @Override
  public void setExpire(@NotNull String key, long expireDuration) {
    if (expireDuration > 0) {
      redisTemplate.expire(key, expireDuration, TimeUnit.SECONDS);
    } else {
      redisTemplate.expire(
          key, redisAppProperties.getDefaultExpiredTimeInSecond(), TimeUnit.SECONDS);
    }
  }

  @Override
  public void setExpire(@NotNull String key, int expireDuration) {
    this.setExpire(key, ((long) expireDuration));
  }

  @Override
  public <T> List<List<T>> getMultiValueList(@NotNull Set<String> keys, Class<T> valueType) {
    List<List<T>> result = new ArrayList<>();
    for (String key : keys) {
      result.add(this.getListValue(key, valueType));
    }
    return result;
  }

  @Override
  public <K, V> List<Map<K, V>> getMultiValueMap(
      Set<String> keys, Class<K> keyType, Class<V> valueType) {
    List<Map<K, V>> result = new ArrayList<>();
    var dataJson = redisTemplate.opsForValue().multiGet(keys);
    if (Objects.nonNull(dataJson)) {
      try {
        for (String value : dataJson) {
          final var map = objectMapper.readValue(value, new TypeReference<Map<K, V>>() {});
          result.add(map);
        }
      } catch (JsonProcessingException e) {
        log.error(ERROR_TO_CONVERTING_TO_JSON.getMessage(), e);
        throw new InternalServerException(
            ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
      }
    }
    return result;
  }

  @Override
  public String getStringValueByKey(String key) {
    return redisTemplate.opsForValue().get(key);
  }

  @Override
  public Set<String> getKeysByPattern(String pattern) {
    if (Objects.isNull(pattern)) {
      return new HashSet<>();
    }
    Set<String> keys = new HashSet<>();
    ScanOptions options =
        ScanOptions.scanOptions().match(pattern).count(RedisKeyConstant.COUNT_SCAN).build();
    try (Cursor<String> cursor = redisTemplate.scan(options)) {
      cursor.forEachRemaining(keys::add);
    }
    return keys;
  }

  @Override
  public <T> List<T> getListValue(String key, Class<T> typeOfList) {
    final List<String> listDataJson = redisTemplate.opsForList().range(key, 0, -1);
    if (listDataJson == null || listDataJson.isEmpty()) {
      return new ArrayList<>();
    }

    return listDataJson.stream()
        .map(
            dataJson -> {
              try {
                return objectMapper.readValue(dataJson, typeOfList);
              } catch (JsonProcessingException e) {
                log.error(ERROR_TO_PARSING_JSON.getMessage(), e);
                throw new InternalServerException(
                    ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
              }
            })
        .toList();
  }

  @Override
  public <K, V> Map<K, V> getMapValue(String key, Class<K> keyType, Class<V> valueType) {
    try {
      var dataJson = redisTemplate.opsForValue().get(key);
      if (StringUtils.isNotEmpty(dataJson)) {
        Map<K, V> result = objectMapper.readValue(dataJson, new TypeReference<>() {});
        result.forEach(
            (keyMap, value) -> {
              var convertedObject = objectMapper.convertValue(value, valueType);
              result.put(keyMap, convertedObject);
            });
        return result;
      }
    } catch (JsonProcessingException e) {
      log.error(ERROR_TO_PARSING_JSON.getMessage(), e);
      throw new InternalServerException(
          ERROR_TO_PARSING_JSON.getMessage(), ERROR_TO_PARSING_JSON.getErrorCode());
    }
    return new HashMap<>();
  }

  @Override
  public void deleteByKeys(Set<String> keys) {
    redisTemplate.delete(keys);
  }
}
