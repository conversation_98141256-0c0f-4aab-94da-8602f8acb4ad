package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.LocAddressJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.CBDSurchargeJPACustom;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface CBDAddressConfigJPARepository extends JpaRepository<LocAddressJPA, String> {

  @Modifying
  @Transactional
  @Query(
      value =
          """
              INSERT INTO loc_address (location_id, address_ref)
              SELECT 1, unnest(:addressRefs)
              ON CONFLICT (location_id, address_ref) DO NOTHING
    """,
      nativeQuery = true)
  void insertBatchOnConflict(@Param("addressRefs") String[] addressRefs);

  @Modifying
  @Transactional
  @Query(
      value =
          """
                DELETE FROM loc_address
                WHERE location_id = 1
                AND address_ref = ANY(:addressRefs)
    """,
      nativeQuery = true)
  void deleteBatchByAddressRef(@Param("addressRefs") String[] addressRefs);

  @Query(
      value =
          """
                  SELECT
                      lsc.location_id AS locationId,
                      l.location_name AS locationName,
                      la.address_ref AS addressRef,
                      lsc.fare_type AS fareType,
                      lsc.charge_by AS chargeBy,
                      lsc.surcharge_value AS surchargeValue,
                      lsc.product_id AS productId,
                      lsc.start_time AS startTime,
                      lsc.end_time AS endTime,
                      lsc.day_indicator AS dayIndicator
                  FROM ngp_me_dynamic_prc.loc_surcharge lsc
                  JOIN ngp_me_dynamic_prc.loc_address la ON lsc.location_id = la.location_id
                  JOIN ngp_me_dynamic_prc."location" l ON lsc.location_id = l.location_id
                  WHERE la.address_ref IN :addressRefs
                  GROUP BY
                      lsc.location_id,
                      l.location_name,
                      la.address_ref,
                      lsc.fare_type,
                      lsc.charge_by,
                      lsc.surcharge_value,
                      lsc.product_id,
                      lsc.start_time,
                      lsc.end_time,
                      lsc.day_indicator
                  """,
      nativeQuery = true)
  List<CBDSurchargeJPACustom> getLocConfigByAddressRefList(
      @Param("addressRefs") String[] addressRefs);
}
