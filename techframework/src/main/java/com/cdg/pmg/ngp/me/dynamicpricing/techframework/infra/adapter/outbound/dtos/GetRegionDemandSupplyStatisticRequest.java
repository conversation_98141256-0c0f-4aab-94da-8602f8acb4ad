package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class GetRegionDemandSupplyStatisticRequest implements Serializable {
  @Serial private static final long serialVersionUID = -2657437411147593771L;

  /** The region ids, if empty, will return all the latest demand supply statistics. */
  private List<Long> regionIds;
}
