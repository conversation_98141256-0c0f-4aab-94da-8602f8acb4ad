package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.AddressOutboundMapper;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AddressServiceAdapter implements AddressService {

  private final AddressOutboundAdapter addressOutboundAdapter;

  private final AddressOutboundMapper addressOutboundMapper;

  @Override
  public GenerateRouteResponse getRoute(GenerateRouteRequest generateRouteRequest) {
    log.info("Feign Address Service get route with request: {}", generateRouteRequest);

    final GenerateRouteOutboundRequest routeOutboundRequest =
        addressOutboundMapper.mapToGenerateRouteOutboundRequest(generateRouteRequest);

    GenerateRouteOutboundResponse route = new GenerateRouteOutboundResponse();
    try {
      final ResponseEntity<GenerateRouteOutboundResponse> routeResponse =
          addressOutboundAdapter.getRoute(routeOutboundRequest);
      if (!ObjectUtils.anyNull(routeResponse, routeResponse.getBody())) {
        route = routeResponse.getBody();
      }
    } catch (Exception e) {
      log.error("Failed to get route", e);
      throw new InternalServerException(
          ErrorEnum.GET_ROUTE_FAIL.getMessage(), ErrorEnum.GET_ROUTE_FAIL.getErrorCode());
    }
    log.info("Feign Address Service get route with response: {}", route);

    return ObjectUtils.isEmpty(route.getRoutes())
        ? null
        : addressOutboundMapper.mapToGenerateRouteResponse(route);
  }

  @Override
  public Optional<EffectiveH3RegionsResponse> getEffectiveH3RegionWithVersion() {
    try {
      Optional<EffectiveH3RegionsResponse> optional =
          Optional.ofNullable(addressOutboundAdapter.getEffectiveH3Regions())
              .map(ResponseEntity::getBody)
              .map(addressOutboundMapper::mapToEffectiveH3RegionsResponse);

      if (log.isDebugEnabled()) {
        if (optional.isPresent()) {
          EffectiveH3RegionsResponse response = optional.get();
          log.debug(
              "Feign Address Service getEffectiveH3Regions regionVersion: {}, effectiveFrom: {}, effectiveTo: {}",
              response.getRegionVersion(),
              response.getEffectiveFrom(),
              response.getEffectiveTo());
        } else {
          log.debug("Feign Address Service getEffectiveH3Regions response is empty");
        }
      }
      return optional;
    } catch (Exception e) {
      log.error("Failed to getEffectiveH3Regions ", e);
      throw new InternalServerException(
          ErrorEnum.GET_EFFECTIVE_H3_REGIONS_FAILED.getMessage(),
          ErrorEnum.GET_EFFECTIVE_H3_REGIONS_FAILED.getErrorCode());
    }
  }

  @Override
  public List<H3RegionComputeResponse> resolveH3Region(final List<H3RegionComputeRequest> request) {
    if (log.isDebugEnabled()) {
      log.debug("Feign Address Service resolveH3Region request: {}", request);
    }

    List<H3RegionComputeOutboundRequest> outBoundRequest =
        addressOutboundMapper.mapToH3RegionComputeOutboundRequest(request);

    try {
      List<H3RegionComputeResponse> response =
          Optional.ofNullable(addressOutboundAdapter.resolveH3Region(outBoundRequest))
              .map(ResponseEntity::getBody)
              .map(H3RegionComputeOutboundResponse::getData)
              .map(addressOutboundMapper::mapToH3RegionComputeResponse)
              .orElseGet(
                  () -> {
                    log.warn("Empty resolveH3Region response");
                    return List.of();
                  });
      if (log.isDebugEnabled()) {
        log.debug("Feign Address Service resolveH3Region response: {}", response);
      }
      return response;
    } catch (Exception e) {
      log.error("Failed to resolveH3Region ", e);
      throw new InternalServerException(
          ErrorEnum.RESOLVE_H3_REGION_BY_GEOGRAPHICAL_COORDINATES_FAILED.getMessage(),
          ErrorEnum.RESOLVE_H3_REGION_BY_GEOGRAPHICAL_COORDINATES_FAILED.getErrorCode());
    }
  }
}
