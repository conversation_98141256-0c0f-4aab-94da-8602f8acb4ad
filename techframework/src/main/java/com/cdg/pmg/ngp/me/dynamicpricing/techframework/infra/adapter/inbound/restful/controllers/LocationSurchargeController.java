package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfigRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.LocationSurchargeConfigTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.LocationSurchargeControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.LocationSurchargeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/** The type Home controller. */
@RestController
@RequiredArgsConstructor
@Slf4j
public class LocationSurchargeController implements LocationSurchargeControllerApi {

  private final LocationSurchargeConfigMapper locationSurchargeConfigMapper;
  private final LocationSurchargeService locationSurchargeService;

  @Override
  public ResponseEntity<GetLocationSurchargesResponse> getLocationSurcharge(
      GetLocationSurchargeRequest getLocationSurchargeRequest) {

    LocationSurchargeConfigRequest locationSurchargeConfigRequest =
        locationSurchargeConfigMapper.map(getLocationSurchargeRequest);
    List<LocationSurchargeConfig> locationSurchargeConfigs =
        locationSurchargeService.getLocationSurcharge(locationSurchargeConfigRequest);

    GetLocationSurchargesResponse result = new GetLocationSurchargesResponse();
    result.setData(locationSurchargeConfigMapper.map(locationSurchargeConfigs));
    return ResponseEntity.ok(result);
  }

  @Override
  public ResponseEntity<LoadConfigsSuccessResponse> reloadLocationSurchargeConfig(
      String configType, String locationId) {
    if (!EnumUtils.isValidEnum(LocationSurchargeConfigTypeEnum.class, configType)) {
      throw new BadRequestException(
          ErrorEnum.INVALID_CONFIG_TYPE.getMessage(), ErrorEnum.INVALID_CONFIG_TYPE.getErrorCode());
    }
    switch (LocationSurchargeConfigTypeEnum.valueOf(configType)) {
      case ALL -> locationSurchargeService.loadAllLocSurchargeConfigs(locationId);
      case SURCHARGE -> locationSurchargeService.loadLocSurchargeConfigs(locationId);
      case ADDRESS -> locationSurchargeService.loadLocSurchargeAddresses(locationId);
    }

    String responseMsg =
        "Reload Location Surcharge - [" + configType + "] reloaded Addresses successfully";
    LoadConfigsSuccessResponse response = new LoadConfigsSuccessResponse();
    response.setData(responseMsg);
    return ResponseEntity.ok(response);
  }
}
