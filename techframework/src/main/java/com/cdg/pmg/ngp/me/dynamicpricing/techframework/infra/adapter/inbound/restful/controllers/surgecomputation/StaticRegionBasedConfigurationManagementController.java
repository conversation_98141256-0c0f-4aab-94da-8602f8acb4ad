package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StaticRegionBasedConfigurationService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticRegionBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationStaticRegionBasedConfigurationManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.StaticRegionBasedConfigurationMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.utilities.RequestUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for managing static region-based configurations. This controller implements the
 * SurgeComputationStaticRegionBasedConfigurationManagementApi interface and delegates business
 * logic to the StaticRegionBasedConfigurationService.
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class StaticRegionBasedConfigurationManagementController
    implements SurgeComputationStaticRegionBasedConfigurationManagementApi {

  private final StaticRegionBasedConfigurationService configurationService;
  private final StaticRegionBasedConfigurationMapper configurationMapper;

  @Override
  public ResponseEntity<StaticBasedConfigurationEffectiveCheckResponse>
      staticRegionBasedConfigurationEffectiveCheck() {
    StaticBasedConfigurationEffectiveCheckEntity entity = configurationService.effectiveCheck();
    StaticBasedConfigurationEffectiveCheckResponse response =
        configurationMapper.mapEffectiveCheckEntityToDto(entity);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StaticBasedConfigurationVersionListResponse>
      getStaticRegionBasedConfigurationVersions(Long modelId) {
    List<StaticBasedConfigurationVersionEntity> versions =
        configurationService.getStaticTimeBasedConfigurationVersions(modelId);

    StaticBasedConfigurationVersionListResponse response =
        new StaticBasedConfigurationVersionListResponse();
    response.setData(configurationMapper.mapVersionEntityToDto(versions));
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StaticRegionBasedConfigurationListResponse>
      getStaticRegionBasedConfigurations(Long modelId, String version) {
    log.info(
        "Getting static region-based configurations by modelId: {}, version: {}", modelId, version);
    List<StaticRegionBasedConfigurationEntity> configEntities =
        configurationService.getStaticRegionBasedConfigurations(modelId, version);
    List<StaticRegionBasedConfiguration> configurations =
        configurationMapper.mapEntityToDto(configEntities);

    StaticRegionBasedConfigurationListResponse response =
        new StaticRegionBasedConfigurationListResponse();
    response.setData(configurations);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<StaticRegionBasedConfigurationBatchCreateResponse>
      batchCreateStaticRegionBasedConfigurations(
          List<StaticRegionBasedConfigurationRequest> request) {
    // Check the user ID from the request header, when operate database will auto get from
    // SpringSecurityAuditorAware
    RequestUtils.getUserIdFromHeader();

    List<StaticRegionBasedConfigurationEntity> configEntities =
        configurationMapper.mapRequestToEntity(request);
    List<StaticRegionBasedConfigurationEntity> createdEntities =
        configurationService.batchCreateStaticRegionBasedConfigurations(configEntities);

    StaticRegionBasedConfigurationBatchCreateResponse response =
        new StaticRegionBasedConfigurationBatchCreateResponse();
    response.setData(configurationMapper.mapEntityToDto(createdEntities));
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<StaticRegionBasedConfigurationResponse>
      getStaticRegionBasedConfigurationById(Long id) {
    log.info("Getting static region-based configuration with ID: {}", id);

    StaticRegionBasedConfigurationEntity configEntity =
        configurationService.getStaticRegionBasedConfigurationById(id);
    if (configEntity == null) {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    StaticRegionBasedConfigurationResponse response = createResponse(configEntity);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<Void> updateStaticRegionBasedConfiguration(
      Long modelId, List<StaticRegionBasedConfigurationRequest> request) {
    // Check the user ID from the request header, when operate database will auto get from
    // SpringSecurityAuditorAware
    String userId = RequestUtils.getUserIdFromHeader();

    log.info(
        "Updating static region-based configuration with modelId: {} and userId: {}",
        modelId,
        userId);

    List<StaticRegionBasedConfigurationEntity> configEntities =
        configurationMapper.mapRequestToEntity(request);
    configurationService.updateStaticRegionBasedConfiguration(modelId, configEntities);

    return ResponseEntity.noContent().build();
  }

  private StaticRegionBasedConfigurationResponse createResponse(
      StaticRegionBasedConfigurationEntity entity) {
    StaticRegionBasedConfiguration model = configurationMapper.mapEntityToDto(entity);

    StaticRegionBasedConfigurationResponse response = new StaticRegionBasedConfigurationResponse();
    response.setData(model);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return response;
  }
}
