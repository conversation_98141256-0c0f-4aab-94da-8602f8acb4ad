package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.PricingRangeConfigJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.custom.PricingRangeCalDemandSurgeJPACustom;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PricingRangeJPARepository extends JpaRepository<PricingRangeConfigJPA, String> {
  @Query(
      value =
          "SELECT "
              + "MAX(dpr1.start_price) * 100 AS surgeLow, "
              + "MAX(dpr1.end_price) * 100 AS surgeHigh, "
              + "MAX(dpr1.step_positive) * 100 AS stepPositive, "
              + "MAX(dpr1.step_negative) * 100 AS stepNegative, "
              + "subTable.zone_id AS zoneId, "
              + "subTable.day AS day, "
              + "subTable.hour AS hour "
              + "FROM DYNP_PRICING_RANGE dpr1 "
              + "JOIN "
              + "(SELECT DISTINCT ON (zone_id) dzm.zone_id AS zone_id, dpr2.day AS day, dpr2.hour AS hour, dpr2.dynp_pricing_range_id AS dynp_pricing_range_id, "
              + "CASE "
              + "WHEN dpr2.day = 'HOL' THEN 1 "
              + "WHEN dpr2.day = 'ALL' THEN 3 "
              + "ELSE 2 "
              + "END as priorityDay, "
              + "CASE "
              + "WHEN dpr2.hour = 'ALL' THEN 2 "
              + "ELSE 1 "
              + "END as priorityHour "
              + "FROM DYNP_PRICING_RANGE dpr2 "
              + "JOIN DYNP_ZONE_MAPPING dzm ON dpr2.dynp_pricing_range_id = dzm.dynp_pricing_range_id "
              + "WHERE "
              + "CURRENT_DATE >= dzm.valid_start_dt "
              + "AND CURRENT_DATE <= dzm.valid_end_dt "
              + "AND dpr2.day IN ( "
              + "CASE WHEN :isHoliday THEN 'HOL' END, to_char(CURRENT_DATE, 'DY'), 'ALL') "
              + "AND dpr2.hour IN (to_char(CURRENT_TIMESTAMP, 'HH24'), 'ALL') "
              + "GROUP BY dzm.zone_id, dpr2.day, dpr2.hour, dpr2.dynp_pricing_range_id "
              + "ORDER BY zone_id, priorityDay, priorityHour) as subTable "
              + "ON dpr1.dynp_pricing_range_id = subTable.dynp_pricing_range_id "
              + "GROUP BY subTable.zone_id, subTable.hour, subTable.day",
      nativeQuery = true)
  List<PricingRangeCalDemandSurgeJPACustom> getPricingRangeConfigs(
      @Param("isHoliday") boolean isHoliday);

  @Query(
      value =
          """
                  SELECT distinct on
                   (zoneId) zoneId,
                   surgeLow,
                   surgeHigh,
                   stepPositive,
                   stepNegative,
                   day,
                   hour
                  FROM  ( SELECT
                    surgeLow,
                    surgeHigh,
                    stepPositive,
                    stepNegative,
                    day,
                    hour,
                    zoneId,
                    my_rank,
                    MAX(my_rank) over (partition by zoneId) as max_rank
                   FROM
                    (
                    select
                      r.start_price * 100 as surgeLow,
                      r.end_price * 100 as surgeHigh,
                      r.step_positive * 100 as stepPositive,
                      r.step_negative * 100 as stepNegative,
                      r.day,
                      r.hour,
                      m.zone_id as zoneId,
                      dense_rank() over (PARTITION BY m.ZONE_ID
                                        ORDER BY
                                         r.day,
                                         r.hour desc,
                                         r.DYNP_PRICING_RANGE_ID) as my_rank
                    FROM
                      DYNP_PRICING_RANGE r
                    JOIN DYNP_ZONE_MAPPING m on
                     r.DYNP_PRICING_RANGE_ID = m.DYNP_PRICING_RANGE_ID
                      WHERE NOW() >= m.valid_start_dt AND NOW() < m.valid_end_dt
                            AND r.day IN (
                                   CASE WHEN :isHoliday THEN 'HOL' END, to_char(TIMEZONE('Asia/Singapore', NOW() ), 'DY'), 'ALL'
                          )
                            AND r.hour IN (TO_CHAR(TIMEZONE('Asia/Singapore',CURRENT_TIMESTAMP), 'HH24'), 'ALL')
                      ) a
                  ) b
                  where
                   b.my_rank = b.max_rank
                      """,
      nativeQuery = true)
  List<PricingRangeCalDemandSurgeJPACustom> getPricingRangeConfigsV2(
      @Param("isHoliday") boolean isHoliday);
}
