package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FareDetail;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.GeneratedRouteEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownRequestEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ValidateFareEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.DynamicPricingControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.dtos.FareDetailInboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.AdditionalChargeFeeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicPricingMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/** The type Home controller. */
@RestController
@RequiredArgsConstructor
@Slf4j
public class DynamicPricingController implements DynamicPricingControllerApi {

  private final DynamicPricingService dynamicPricingService;

  private final DemandSupplyService demandSupplyService;

  private final DynamicPricingMapper mapper;

  private static final String UPDATED_DYNP_SURGE_SUCCESS_MSG =
      "Update demand supply surge successfully";

  private static final String UPDATED_DYNP_SURGE_NGP_SUCCESS_MSG =
      "Update demand supply surge ngp successfully";

  @Override
  public ResponseEntity<GetEstimatedFareResponse> getMultiFare(
      GetEstimatedFareInboundRequest getEstimatedFareInboundRequest) {
    log.info("CONTROLLER - Start getMultiFare - request: {}", getEstimatedFareInboundRequest);

    final MultiFareRequestQuery requestQuery =
        mapper.mapToEstFareRequestQuery(getEstimatedFareInboundRequest);

    final MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFare(requestQuery);

    final GetEstimatedFareResponse response = new GetEstimatedFareResponse();
    response.data(mapper.mapToGetEstimatedFareInboundResponse(multiFareResponse));

    return ResponseEntity.ok(response);
  }

  @RequestMapping(
      method = RequestMethod.POST,
      value = "/v1.0/pricing/multi-fare-detail",
      produces = {"application/json"},
      consumes = {"application/json"})
  public ResponseEntity<FareDetailInboundResponse> getMultiFareDetail(
      @RequestBody MultiFareRequestQuery multiFareRequestQuery) {
    log.info("CONTROLLER - Start getMultiFareDetail - request: {}", multiFareRequestQuery);

    final FareDetail fareDetail = dynamicPricingService.getMultiFareDetail(multiFareRequestQuery);
    final FareDetailInboundResponse fareDetailInboundResponse =
        mapper.mapToFareDetailInboundResponse(fareDetail);
    return ResponseEntity.ok(fareDetailInboundResponse);
  }

  @Override
  public ResponseEntity<GetEstimatedFareResponse> getMultiFareById(String fareId) {
    log.info("CONTROLLER - Start get multi-fare by requested fare ID: {}", fareId);
    final MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFareByFareId(fareId);
    final GetEstimatedFareResponse response = new GetEstimatedFareResponse();
    response.data(mapper.mapToGetEstimatedFareInboundResponse(multiFareResponse));
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StoreFareBreakdownInboundResponse> storeFareBreakdown(
      StoreFareBreakdownInboundRequest storeFareBreakdownInboundRequest) {
    final StoreFareBreakdownCommandRequest request =
        mapper.mapToStoreFareBreakdownRequestQuery(storeFareBreakdownInboundRequest);

    final StoreFareBreakdownCommandResponse queryResponse =
        dynamicPricingService.storeFareBreakdownDetail(request);

    final StoreFareBreakdownInboundResponse response = new StoreFareBreakdownInboundResponse();
    response.data(mapper.mapToStoreFareBreakdownInboundResponseData(queryResponse));

    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<ValidateFareResponse> verifyFare(ValidateFareRequest verifyFareRequest) {
    log.info("CONTROLLER - Start to validate fare - request: {}", verifyFareRequest);
    final ValidateFareEntity request = mapper.mapToValidateFareEntity(verifyFareRequest);

    final ValidateFareResponseData data = new ValidateFareResponseData();
    data.setIsValidFare(dynamicPricingService.validateFare(request));

    final ValidateFareResponse response = new ValidateFareResponse();
    response.setData(data);

    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<GetGeneratedRouteResponse> getGeneratedRoute(String tripId) {
    log.info("Dynamic Pricing Controller - Start get generated route - tripId: {}", tripId);
    if (Objects.isNull(tripId)) {
      log.error("Invalid trip id");
      throw new BadRequestException(INVALID_TRIP_ID.getMessage(), INVALID_TRIP_ID.getErrorCode());
    }
    GeneratedRouteEntity generatedRouteEntity =
        dynamicPricingService.getGeneratedRouteByTripId(tripId);
    GeneratedRoute data = mapper.mapGeneratedRouteEntityToGeneratedRoute(generatedRouteEntity);
    GetGeneratedRouteResponse response = new GetGeneratedRouteResponse();
    response.setData(data);
    log.info("Dynamic Pricing Controller - End get generated route - tripId: {}", tripId);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<UpdateDemandSuccessResponse> updateDynamicSurge() {
    log.info("CONTROLLER - Start to update dynamic surge");
    demandSupplyService.calculateDemandSupplySurge();

    final UpdateDemandSuccessResponse response = new UpdateDemandSuccessResponse();
    response.setData(UPDATED_DYNP_SURGE_SUCCESS_MSG);

    log.info("Dynamic Pricing Controller - End update dynamic surge");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<SearchFareBreakdownInboundResponse> searchFareBreakdown(
      SearchFareBreakdownInboundRequest searchFareBreakdownRequest) {
    log.info("CONTROLLER - Start to search fare breakdown");
    final SearchFareBreakdownRequestEntity request =
        mapper.mapToSearchBreakdownRequest(searchFareBreakdownRequest);
    final SearchFareBreakdownResponse searchFareBreakdownResponse =
        dynamicPricingService.searchFareBreakdown(request);
    final SearchFareBreakdownInboundResponse response =
        mapper.mapToSearchFareBreakdownResponse(searchFareBreakdownResponse);
    log.info("Dynamic Pricing Controller - End search fare breakdown");
    return ResponseEntity.ok(response);
  }

  /**
   * Invoked by the AWS EventBridge Scheduler to update dynamic surge values. This method triggers
   * the recalculation of dynamic surge factors via the {@link
   * DemandSupplyService#calculateDemandSupplySurgeNgp()} method.
   *
   * <p>Upon successful completion, it returns a response entity containing a success message,
   * indicating that the dynamic surge update for NGP has been completed.
   *
   * @return a {@link ResponseEntity} containing an {@link UpdateDemandSuccessResponse} with the
   *     success message after successfully updating the NGP dynamic surge.
   */
  @Override
  public ResponseEntity<UpdateDemandSuccessResponse> updateDynamicSurgeV2() {
    log.info("Start to update dynamic surge NGP-- V2");
    demandSupplyService.calculateDemandSupplySurgeNgp();

    final UpdateDemandSuccessResponse response = new UpdateDemandSuccessResponse();
    response.setData(UPDATED_DYNP_SURGE_NGP_SUCCESS_MSG);

    log.info("Dynamic Pricing Controller - End update dynamic surge NGP");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<AdditionalChargeFeesResponse> getAdditionalChargeFeesByCondition(
      String fareId, Integer vehTypeId, String productTypeId) {

    List<AdditionalChargeFeeData> additionalChargeFeeDataList =
        dynamicPricingService.getAdditionalChargeFeesByCondition(fareId, vehTypeId, productTypeId);

    if (ObjectUtils.isEmpty(additionalChargeFeeDataList)) {
      log.warn(
          "Not found additionalChargeFees by condition,fareId={},vehTypeId={},productTypeId={} .",
          fareId,
          vehTypeId,
          productTypeId);
      return ResponseEntity.notFound().build();
    } else {

      List<AdditionalChargeFee> additionalChargeFeeList =
          AdditionalChargeFeeMapper.mapToAdditionalChargeFeeDataList(additionalChargeFeeDataList);

      AdditionalChargeFeesResponse additionalChargeFeesResponse =
          new AdditionalChargeFeesResponse();
      additionalChargeFeesResponse.setData(additionalChargeFeeList);

      log.info(
          "The return additional Charge Fees, additionalChargeFeesResponse={} .",
          additionalChargeFeesResponse);

      return ResponseEntity.ok(additionalChargeFeesResponse);
    }
  }
}
