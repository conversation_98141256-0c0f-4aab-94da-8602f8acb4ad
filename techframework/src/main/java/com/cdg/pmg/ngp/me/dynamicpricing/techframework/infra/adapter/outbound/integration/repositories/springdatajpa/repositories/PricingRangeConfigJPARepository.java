package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.PricingRangeConfigJPA;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PricingRangeConfigJPARepository
    extends JpaRepository<PricingRangeConfigJPA, Integer> {
  @Query(
      value =
          "SELECT pr.dynp_pricing_range_id, "
              + "pr.start_price, pr.end_price, "
              + "pr.step, pr.refresh_period, "
              + "pr.quote_valid_period, "
              + "pr.day, "
              + "pr.hour, "
              + "pr.step_positive, "
              + "pr.step_negative, "
              + "pr.is_enabled, "
              + "pr.created_by, "
              + "pr.created_dt, "
              + "pr.updated_by, "
              + "pr.updated_dt "
              + "FROM dynp_pricing_range pr "
              + "WHERE pr.is_enabled = 1 "
              + "ORDER BY pr.updated_dt DESC ",
      nativeQuery = true)
  List<PricingRangeConfigJPA> getPricingRangeConfigs();

  @Query(
      value =
          "SELECT pr.dynp_pricing_range_id, "
              + "pr.start_price, pr.end_price, "
              + "pr.step, pr.refresh_period, "
              + "pr.quote_valid_period, "
              + "pr.day, "
              + "pr.hour, "
              + "pr.step_positive, "
              + "pr.step_negative, "
              + "pr.is_enabled, "
              + "pr.created_by, "
              + "pr.created_dt, "
              + "pr.updated_by, "
              + "pr.updated_dt "
              + "FROM dynp_pricing_range pr "
              + "WHERE pr.is_enabled = 1 "
              + "AND pr.day = :day "
              + "AND pr.hour = :hour "
              + "ORDER BY pr.updated_dt DESC ",
      nativeQuery = true)
  List<PricingRangeConfigJPA> getPricingRangeConfigsByDayAndHour(String day, String hour);
}
