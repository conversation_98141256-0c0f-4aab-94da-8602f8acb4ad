package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_REGION_ID;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_REGION_MODEL_DISTRIBUTION_FOR_REGIONG;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.DynamicPricingRegionBasedService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.ModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.RegionModelDistributionService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.TemporaryUatTestingControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicPricingMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetEstimatedFareInboundRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.GetEstimatedFareResponseUAT;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class TemporaryUatTestingController implements TemporaryUatTestingControllerApi {

  private final DynamicPricingService dynamicPricingService;
  private final RequestCounterService requestCounterService;
  private final AddressService addressService;
  private final DynamicPricingMapper mapper;
  private final DynamicPricingRegionBasedService dynamicPricingRegionBasedService;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;
  private final RegionModelDistributionService regionModelDistributionService;
  private final ModelService modelService;

  @Override
  public ResponseEntity<GetEstimatedFareResponseUAT> getMultiFareUAT(
      GetEstimatedFareInboundRequest getEstimatedFareInboundRequest) {
    log.info(
        "[getMultiFareUAT] Start getMultiFare for UAT test - request: {}",
        getEstimatedFareInboundRequest);

    Instant now = Instant.now();

    final MultiFareRequestQuery requestQuery =
        mapper.mapToEstFareRequestQuery(getEstimatedFareInboundRequest);

    H3RegionComputeResponse region = resolveRegionId(getEstimatedFareInboundRequest);

    DynamicPricingSurgeConfig dynamicPricingSurgeConfig =
        configurationServiceOutboundPort.getDynamicPricingSurgeConfig();

    final SurgeAreaTypeEnum areaType;
    final MultiFareResponse multiFareResponse;
    if (dynamicPricingSurgeConfig.isRandomToRegion()) {
      log.info("[getMultiFareUAT] getMultiFare based on region");
      areaType = SurgeAreaTypeEnum.REGION;

      ModelPercentage model = selectModelByCumulativeWeight(region, getEstimatedFareInboundRequest);
      requestQuery.setRegionId(region.getRegionId());
      requestQuery.setModelId(model.getModelId());
      requestQuery.setModelName(model.getModelName());
      multiFareResponse = dynamicPricingRegionBasedService.getMultiFare(requestQuery);
      multiFareResponse.setModelId(model.getModelId());
      multiFareResponse.setModelName(model.getModelName());
    } else {
      log.info("[getMultiFareUAT] getMultiFare based on zone");
      areaType = SurgeAreaTypeEnum.ZONE;
      multiFareResponse = dynamicPricingService.getMultiFare(requestQuery);
    }
    multiFareResponse.setAreaType(areaType.getValue());
    multiFareResponse.setRegionId(region.getRegionId());

    GetFareCountEntity getFareCountEntity =
        new GetFareCountEntity(
            areaType,
            region.getRegionId(),
            region.getRegionVersion(),
            requestQuery.getModelId(),
            now);

    // Record the request count based on region id
    requestCounterService.recordEndpointRequest(
        RequestCountConstant.MULTI_FARE, getFareCountEntity);

    final GetEstimatedFareResponseUAT response = new GetEstimatedFareResponseUAT();
    response.data(mapper.mapToGetEstimatedFareResponseUAT(multiFareResponse));

    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<GetEstimatedFareResponseUAT> getMultiFareByIdUAT(String fareId) {
    log.info("[getMultiFareByIdUAT] Start get multi-fare by requested fare ID: {}", fareId);
    final MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFareByFareId(fareId);
    final GetEstimatedFareResponseUAT response = new GetEstimatedFareResponseUAT();
    response.data(mapper.mapToGetEstimatedFareResponseUAT(multiFareResponse));
    return ResponseEntity.ok(response);
  }

  private ModelPercentage selectModelByCumulativeWeight(
      final H3RegionComputeResponse region, final GetEstimatedFareInboundRequest request) {
    Optional<RegionModelDistributionEntity> distributionOptional =
        regionModelDistributionService.getEffectiveRegionModelDistribution(region.getRegionId());

    RegionModelDistributionEntity effectiveRegionModelDistribution =
        distributionOptional.orElseThrow(
            () ->
                new BadRequestException(
                    MessageFormat.format(
                        NOT_FOUND_REGION_MODEL_DISTRIBUTION_FOR_REGIONG.getMessage(),
                        region.getRegionId()),
                    NOT_FOUND_REGION_MODEL_DISTRIBUTION_FOR_REGIONG.getErrorCode()));

    List<ModelPercentage> models =
        effectiveRegionModelDistribution.getModels().stream()
            .sorted(Comparator.comparing(ModelPercentage::getModelId))
            .toList();

    ModelPercentage modelPercentage = null;

    double randomValue = ThreadLocalRandom.current().nextDouble(1, 101);
    double cumulativeWeight = 0.0;
    for (ModelPercentage model : models) {
      cumulativeWeight += model.getPercentage().doubleValue();
      if (randomValue <= cumulativeWeight) {
        modelPercentage = model;
        break;
      }
    }

    if (modelPercentage == null) {
      // Theoretically, will never enter here.
      log.warn("[selectModelByCumulativeWeight] use default first model");
      modelPercentage = models.get(0);
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[selectModelByCumulativeWeight] mobile: {}, pickupAddressRef: {}, pickupAddressLat: {}, pickupAddressLng: {}, regionId: {}, regionVersion: {}, modelRandomValue: {}, modelId: {}, modelName: {}",
          request.getMobile(),
          request.getPickupAddressRef(),
          request.getPickupAddressLat(),
          request.getPickupAddressLng(),
          region.getRegionId(),
          region.getRegionVersion(),
          randomValue,
          modelPercentage.getModelId(),
          modelPercentage.getModelName());
    }

    String modelName = modelService.getSurgeComputationModelName(modelPercentage.getModelId());
    modelPercentage.setModelName(modelName);

    return modelPercentage;
  }

  private H3RegionComputeResponse resolveRegionId(
      final GetEstimatedFareInboundRequest getEstimatedFareInboundRequest) {
    H3RegionComputeRequest request =
        mapper.mapToH3RegionComputeRequest(getEstimatedFareInboundRequest);
    Optional<H3RegionComputeResponse> regionIdOptional = addressService.resolveH3Region(request);

    return regionIdOptional.orElseThrow(
        () ->
            new NotFoundException(
                MessageFormat.format(
                    NOT_FOUND_REGION_ID.getMessage(),
                    String.valueOf(request.getLat()),
                    String.valueOf(request.getLng())),
                NOT_FOUND_REGION_ID.getErrorCode()));
  }
}
