package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.adapter;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.DemandSupplyStatisticsResponseV2;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.RegionDemandSupplyStatistic;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FleetAnalyticService;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.mapper.FleetAnalOutboundMapper;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FleetAnalyticServiceAdapter implements FleetAnalyticService {
  private final FleetAnalyticOutboundAdapter fleetAnalyticOutboundAdapter;
  private final FleetAnalOutboundMapper mapper;

  @Override
  public List<DemandSupplyStatisticsResponse> getDemandSuppyStatistics() {
    log.info("Get new demand supply statistic");
    List<DemandSupplyStatisticsOutbound> demandSupplyList = new ArrayList<>();
    try {
      final ResponseEntity<DemandSupplyStatisticsOutboundResponse> demandSupplyResponse =
          fleetAnalyticOutboundAdapter.getDemandSupplyStatistics();
      if (!ObjectUtils.anyNull(demandSupplyResponse, demandSupplyResponse.getBody())) {
        final DemandSupplyStatisticsOutboundResponse response = demandSupplyResponse.getBody();
        if (Objects.nonNull(response.getData())) {
          demandSupplyList = response.getData();
        }
      }
    } catch (Exception e) {
      log.error("Failed to new demand supply statistic", e);
    }

    return mapper.mapToDemandSupplyStatisticsResponseList(demandSupplyList);
  }

  @Override
  public List<DemandSupplyStatisticsResponseV2> getDemandSupplyStatisticsV2() {
    log.info("Get new demand supply statistics");
    List<DemandSupplyStatisticsOutboundV2> demandSupplyList = new ArrayList<>();
    try {
      final ResponseEntity<DemandSupplyStatisticsOutboundV2Response> demandSupplyResponse =
          fleetAnalyticOutboundAdapter.getDemandSupplyStatisticsV2();
      if (!ObjectUtils.anyNull(demandSupplyResponse, demandSupplyResponse.getBody())) {
        final DemandSupplyStatisticsOutboundV2Response response = demandSupplyResponse.getBody();
        if (Objects.nonNull(response) && Objects.nonNull(response.getData())) {
          demandSupplyList = response.getData();
        }
      }
    } catch (Exception e) {
      log.error("Failed to new demand supply statistics", e);
    }

    return mapper.mapToDemandSupplyStatisticsResponseListV2(demandSupplyList);
  }

  @Override
  public List<RegionDemandSupplyStatistic> getRegionDemandSupplyStatistics() {
    try {
      return Optional.ofNullable(
              fleetAnalyticOutboundAdapter.getLatestRegionDemandSupplyStatistics(
                  new GetRegionDemandSupplyStatisticRequest()))
          .map(ResponseEntity::getBody)
          .map(GetLatestRegionDemandSupplyStatisticsResponse::getData)
          .map(mapper::mapToRegionDemandSupplyStatisticList)
          .orElseGet(
              () -> {
                log.warn("Empty region demand supply statistics response");
                return Collections.emptyList();
              });
    } catch (Exception e) {
      log.error("Failed to get region demand supply statistics", e);
      return Collections.emptyList();
    }
  }
}
