package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.commands.NewPricingModelCommand;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.ListConfigObject;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.CmsConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.ConfigManagementService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.cbdcharge.CBDAddressConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.NewPricingModelConfigEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.CBDAddressMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.CmsPricingConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicSurgeDataMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.LocationSurchargeConfigMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ExcludeBookingChannelListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ExcludeBookingChannelRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.ExcludeBookingChannelResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.JobStatusListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.JobStatusRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.JobStatusResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigListResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelConfigResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.NewPricingModelRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class ConfigManageControllerTest {

  @Mock private ConfigManagementService service;

  @InjectMocks ConfigManageController controller;
  @Mock ConfigManagementService configManagementService;
  private static final String LOAD_CONFIGS_SUCCESS_MSG = "Loaded configs successful!";
  @Mock CmsConfigService cmsConfigService;
  @Mock DynamicSurgeDataMapper dynamicSurgeDataMapper;
  @Mock CmsPricingConfigMapper cmsPricingConfigMapper;
  @Mock LocationSurchargeConfigMapper mapper;
  @Mock LocationSurchargeService locationSurchargeService;
  @Mock CBDAddressMapper cbdAddressMapper;
  @Mock CBDAddressConfigService cbdAddressConfigService;

  @BeforeEach
  void setUp() {
    controller =
        new ConfigManageController(
            mapper,
            configManagementService,
            cmsConfigService,
            cmsPricingConfigMapper,
            dynamicSurgeDataMapper,
            locationSurchargeService,
            cbdAddressMapper,
            cbdAddressConfigService);
  }

  @Test
  void whenLoadConfigs_returnSuccess() {
    Assertions.assertDoesNotThrow(() -> controller.loadConfigs());
  }

  @ParameterizedTest
  @CsvSource({
    "ALL",
    "FLAT_FARE",
    "FARE_TYPE",
    "COMPANY_HOLIDAY",
    "LOCATION_SURCHARGE",
    "DYNP_SURGE"
  })
  void givenNormalCondition_whenReloadConfigs_returnSuccess(String configType) {
    Assertions.assertDoesNotThrow(() -> controller.reloadConfigs(configType));
  }

  @Test
  void whenGetNewPricingModelConfig_notThrowException() {
    // Arrange
    NewPricingModelConfigListResponse expectedResponse = new NewPricingModelConfigListResponse();
    when(cmsConfigService.getListNewPricingModelConfigEntityInCms())
        .thenReturn(Collections.singletonList(new NewPricingModelConfigEntity()));
    when(cmsPricingConfigMapper.toNewPricingModelItemResponse(any())).thenReturn(expectedResponse);
    // Act
    ResponseEntity<NewPricingModelConfigListResponse> response =
        controller.getNewPricingModelConfig();
    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenCreateNewPricingModelConfig_notThrowException() {
    // Arrange
    NewPricingModelRequest newPricingModelRequest = new NewPricingModelRequest();
    NewPricingModelConfigResponse expectedResponse = new NewPricingModelConfigResponse();
    when(cmsPricingConfigMapper.toUpdateNewPricingModelCommand(newPricingModelRequest))
        .thenReturn(new NewPricingModelCommand());
    when(cmsConfigService.createNewPricingConfigModelConfigEntityInCms(
            any(NewPricingModelCommand.class)))
        .thenReturn(new NewPricingModelConfigEntity());
    when(cmsPricingConfigMapper.toNewPricingModelResponse(any(NewPricingModelConfigEntity.class)))
        .thenReturn(expectedResponse);

    // Act
    ResponseEntity<NewPricingModelConfigResponse> response =
        controller.createNewPricingModelConfig(newPricingModelRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenUpdateNewPricingModelConfig_notThrowException() {
    // Arrange
    NewPricingModelRequest updateNewPricingModelRequest = new NewPricingModelRequest();
    NewPricingModelConfigResponse expectedResponse = new NewPricingModelConfigResponse();
    when(cmsPricingConfigMapper.toUpdateNewPricingModelCommand(updateNewPricingModelRequest))
        .thenReturn(new NewPricingModelCommand());
    when(cmsConfigService.updateNewPricingModelConfigEntityInCms(any()))
        .thenReturn(new NewPricingModelConfigEntity());
    when(cmsPricingConfigMapper.toNewPricingModelResponse(any(NewPricingModelConfigEntity.class)))
        .thenReturn(expectedResponse);

    // Act
    ResponseEntity<NewPricingModelConfigResponse> response =
        controller.updateNewPricingModelConfig(updateNewPricingModelRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenAddExcludeBookingChannel_notThrowException() {
    // Arrange
    ExcludeBookingChannelRequest excludeBookingChannelRequest = new ExcludeBookingChannelRequest();
    ExcludeBookingChannelResponse expectedResponse = new ExcludeBookingChannelResponse();
    when(cmsPricingConfigMapper.toExcludeBookingChannelResponse(excludeBookingChannelRequest))
        .thenReturn(expectedResponse);

    // Act
    ResponseEntity<ExcludeBookingChannelResponse> response =
        controller.addExcludeBookingChannel(excludeBookingChannelRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenDeleteExcludeBookingChannel_notThrowException() {
    // Arrange
    ExcludeBookingChannelRequest excludeBookingChannelRequest = new ExcludeBookingChannelRequest();
    ExcludeBookingChannelResponse expectedResponse = new ExcludeBookingChannelResponse();
    when(cmsPricingConfigMapper.toExcludeBookingChannelResponse(excludeBookingChannelRequest))
        .thenReturn(expectedResponse);

    // Act
    ResponseEntity<ExcludeBookingChannelResponse> response =
        controller.deleteExcludeBookingChannel(excludeBookingChannelRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenGetExcludeBookingChannel_notThrowException() {
    // Arrange
    List<String> bookingChannels = new ArrayList<>();
    bookingChannels.add("Channel1");
    bookingChannels.add("Channel2");
    when(cmsConfigService.getConfigsByDescriptionKey(any()))
        .thenReturn(ListConfigObject.builder().items(bookingChannels).build());
    // Act
    ResponseEntity<ExcludeBookingChannelListResponse> response =
        controller.getExcludeBookingChannel();
    // Assert
    assertNotNull(response.getBody());
    assertNotNull(response.getBody().getItems());
    assertEquals(2, response.getBody().getItems().size());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenAddJobStatus_notThrowException() {
    // Arrange
    JobStatusRequest jobStatusRequest = new JobStatusRequest();
    JobStatusResponse expectedResponse = new JobStatusResponse();
    when(cmsPricingConfigMapper.toJobStatusReponse(jobStatusRequest)).thenReturn(expectedResponse);

    // Act
    ResponseEntity<JobStatusResponse> response = controller.addJobStatus(jobStatusRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenDeleteJobStatus_notThrowException() {
    // Arrange
    JobStatusRequest jobStatusRequest = new JobStatusRequest();
    JobStatusResponse expectedResponse = new JobStatusResponse();
    when(cmsPricingConfigMapper.toJobStatusReponse(jobStatusRequest)).thenReturn(expectedResponse);

    // Act
    ResponseEntity<JobStatusResponse> response = controller.deleteJobStatus(jobStatusRequest);

    // Assert
    assertEquals(expectedResponse, response.getBody());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void whenGetJobStatus_notThrowException() {
    // Arrange
    List<String> jobStatuses = new ArrayList<>();
    jobStatuses.add("Status1");
    jobStatuses.add("Status2");
    when(cmsConfigService.getConfigsByDescriptionKey(any()))
        .thenReturn(ListConfigObject.builder().items(jobStatuses).build());

    // Act
    ResponseEntity<JobStatusListResponse> response = controller.getJobStatus();

    // Assert
    assertNotNull(response.getBody());
    assertNotNull(response.getBody().getItems());
    assertEquals(2, response.getBody().getItems().size());
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void givenData_whenGetLocationSurchargeConfig_thenReturnSuccess() {
    Assertions.assertDoesNotThrow(() -> controller.getLocationSurchargeConfig(any()));
  }

  @Test
  void givenData_whenUpdateCbdAddress_thenReturnSuccess() {
    Assertions.assertDoesNotThrow(() -> controller.updateCbdAddress(any()));
  }
}
