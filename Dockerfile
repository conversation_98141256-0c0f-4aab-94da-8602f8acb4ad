# syntax=docker/dockerfile:1
# Provide ECR URI
ARG ECR_URI
# Download OpenTelemetryAgent
FROM ${ECR_URI}/curl:8.8.0 AS GUANCE_AGENT
ARG GUANCE_AGENT_URL="https://static.guance.com/dd-image/dd-java-agent.jar"
RUN curl --silent --fail -L ${GUANCE_AGENT_URL} -o "/tmp/dd-java-agent.jar"

FROM ${ECR_URI}/gradle:7.6.1-jdk17 AS build
ARG SONAR_TOKEN
ARG SONAR_HOST
ARG SONAR_PROJECT
ARG SONAR_ORGANIZATION
ARG SONAR_BRANCH
WORKDIR /app
COPY . .
RUN gradle clean build testCodeCoverageReport sonar -x spotlessCheck -x spotlessApply -Dsonar.login=$SONAR_TOKEN -Dsonar.host.url=$SONAR_HOST -Dsonar.projectKey=$SONAR_PROJECT -Dsonar.organization=$SONAR_ORGANIZATION -Dsonar.branch.name=$SONAR_BRANCH -Dorg.gradle.jvmargs="-XX:MetaspaceSize=1024M -XX:MaxMetaspaceSize=1024M"

FROM ${ECR_URI}/amazoncorretto-jdk:17-alpine3.18-jdk AS deploy
ARG profile
ENV APP_HOME /app
ENV APP_USER appuser
ENV JAVA_OPTS="${JAVA_OPTS} -Dspring.profiles.active=$profile"

WORKDIR /app

COPY ./redis_ca.pem cacert.pem
RUN keytool -import -noprompt -trustcacerts -alias rediscloudcacert -file ./cacert.pem -cacerts -storepass changeit

COPY techframework/build/libs/techframework-0.0.1-SNAPSHOT.jar ./service.jar
COPY --from=GUANCE_AGENT /tmp/dd-java-agent.jar ./dd-java-agent.jar
COPY startup.sh ./startup.sh
RUN chmod -x ./startup.sh

RUN adduser -S -H $APP_USER
RUN chown -R $APP_USER $APP_HOME
USER $APP_USER

EXPOSE 8080
ENTRYPOINT ["sh", "./startup.sh"]
